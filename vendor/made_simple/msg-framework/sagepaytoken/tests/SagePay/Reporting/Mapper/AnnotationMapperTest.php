<?php

namespace SagePayToken\Tests\Reporting\Mapper;

use Doctrine\Common\Annotations\AnnotationReader;
use SagePayToken\Reporting\Annotations\SpField;
use SagePayToken\Reporting\Request\TokenDetails as TokenDetailsRequest;
use SagePayToken\Reporting\Response\TokenDetails as TokenDetailsResponse;
use SagePayToken\Reporting\SagePayFactory;
use PHPUnit\Framework\TestCase;

class AnnotationMapperTest extends TestCase
{
    /**
     * @var AnnotationMapper
     */
    protected $object;

    /**
     * Sets up the fixture, for example, opens a network connection.
     * This method is called before a test is executed.
     */
    protected function setUp(): void
    {
        SagePayFactory::register(VENDOR_DIR);

        $annotationName = get_class(new SpField());
        $this->object = new AnnotationMapper(new AnnotationReader(), $annotationName);
    }

    /**
     * Tears down the fixture, for example, closes a network connection.
     * This method is called after a test is executed.
     */
    protected function tearDown(): void
    {
        
    }

    /**
     * @covers SagePayToken\Reporting\Mapper\AnnotationMapper::objectToArray
     */
    public function testObjectToArray()
    {
        $request = new TokenDetailsRequest('abcdef');
        $requestArray = $this->object->objectToArray($request);

        $this->assertNotEmpty($requestArray);
        $this->assertEquals($requestArray['command'], $request->getCommand());
        $this->assertEquals($requestArray['token'], $request->getToken());
    }

    /**
     * @covers SagePayToken\Reporting\Mapper\AnnotationMapper::arrayToObject
     */
    public function testArrayToObject()
    {
        $responseArray = array(
            'errorcode' => '0000',
            'token' => 'abcdef'
        );
        $response = new TokenDetailsResponse();
        $this->object->arrayToObject($responseArray, $response);

        $this->assertEquals($responseArray['errorcode'], $response->getErrorCode());
        $this->assertEquals($responseArray['token'], $response->getToken());
    }

}

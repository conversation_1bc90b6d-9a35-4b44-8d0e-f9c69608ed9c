<?php

use tests\helpers\VoucherHelper;
use PHPUnit\Framework\TestCase;
use Libs\Basket;
use Models\Products\BasketProduct;
use Models\Products\RegisterOffice;

class BasketTest extends TestCase
{
    /**
     * @var Basket
     */
    private Basket $object;

    public function setUp(): void
    {
        $this->object = new Basket('test_namespace');
        $this->object->clear(TRUE);
    }

    public function testTotalPrice()
    {
        $this->assertEquals($this->object->getTotalPrice(), 0);
        $this->addProduct();
        // vat added
        $this->assertEquals($this->object->getTotalPrice(), 100 * (1 + Basket::VAT));
    }

    public function testTotalPriceWithCredits()
    {
        /*
         * AssertEquals parameters was inverted. I wonder if this have ever really tested this.
         * it should be: assertEquals(Expected, Actual)
         */
        $this->addProduct();
        $this->object->setCredit(20);
        $this->assertEquals(100, $this->object->getTotalPrice());
        $this->object->setCredit(120);
        $this->assertEquals(0, $this->object->getTotalPrice());
        // is this correct ?
        $this->object->setCredit(130);
        $this->assertEquals(120, $this->object->getTotalPrice());
    }

    public function testIsUsingCredit()
    {
        $this->assertFalse($this->object->isUsingCredit());
        $this->object->setCredit(100);
        $this->assertTrue($this->object->isUsingCredit());
        $this->object->setCredit(0);
        $this->assertFalse($this->object->isUsingCredit());
    }

    public function testInsufficientCredit()
    {
        $this->addProduct();
        $this->assertFalse($this->object->isInsufficientCredit());
        $this->object->setCredit(100);
        $this->assertTrue($this->object->isInsufficientCredit());
        $this->object->setCredit(120);
        $this->assertFalse($this->object->isInsufficientCredit());
    }

    public function testIsInsufficientCredit()
    {
        $this->assertFalse($this->object->isInsufficientCredit());
        
        $this->addProduct();
        $this->assertFalse($this->object->isInsufficientCredit());

        $this->object->setCredit(10);
        $this->assertTrue($this->object->isInsufficientCredit());

        $this->object->setCredit(120);
        $this->assertFalse($this->object->isInsufficientCredit());

        $this->object->setCredit(1000);
        $this->assertFalse($this->object->isInsufficientCredit());
    }

    public function testIsFreePurchase()
    {
        $this->assertFalse($this->object->isFreePurchase());
        $this->addProduct();
        $this->assertFalse($this->object->isFreePurchase());

        $voucher = VoucherHelper::createDiscount(uniqid('a', TRUE), 100);
        $voucher->save();
        $this->object->setVoucher($voucher->getId());
        $this->assertTrue($this->object->isFreePurchase());

        $this->object->removeVoucher();
        $this->assertFalse($this->object->isFreePurchase());

        $voucher = VoucherHelper::createDiscount(uniqid('b', TRUE), 99);
        $voucher->save();
        $this->object->setVoucher($voucher->getId());
        $this->assertFalse($this->object->isFreePurchase());
    }

    private function addProduct()
    {
        $product = new BasketProduct(RegisterOffice::PRODUCT_BUSINESS_TOOLKIT);
        $product->setPrice(100);
        $product->setStatusId(BasketProduct::STATUS_PUBLISHED);
        $this->object->add($product);
    }
}
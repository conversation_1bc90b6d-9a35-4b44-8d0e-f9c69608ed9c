<?php

namespace tests\project\models\Repositories;

use Entities\Feefo;
use Repositories\FeefoRepository;
use TestModule\Annotations\Inject;
use TestModule\Helpers\DatabaseHelper;
use TestModule\PhpUnit\TestCase;
use tests\helpers\EntityHelper;
use Utils\Date;

class FeefoRepositoryTest extends TestCase
{
    /**
     * @var DatabaseHelper
     */
    private $databaseHelper;

    /**
     * @var Feefo[]
     */
    private $feefoLeads;

    /**
     * @var FeefoRepository
     */
    private $repository;

    /**
     * @Inject({
     *     "databaseHelper"="test_module.helpers.database_helper",
     *     "repository"="repositories.feefo_repository"
     * })
     */
    public function setUpDependencies(
        DatabaseHelper $databaseHelper,
        FeefoRepository $repository
    )
    {
        $this->databaseHelper = $databaseHelper;
        $this->repository = $repository;
        $this->clearTables();
        $this->init();
    }

    /**
     * @covers \Repositories\FeefoRepository::getEligibleOrders
     */
    public function testGetEligibleOrders()
    {
        $items = $this->repository->getEligibleOrders();
        $count = 0;
        foreach ($items as $row) {
            /** @var Feefo $feefo */
            $feefo = $row[0];
            $this->assertEquals($this->feefoLeads['eligible'], $feefo);
            $count++;
        }
        $this->assertEquals(1, $count);
    }

    public function tearDown(): void
    {
        $this->clearTables();
    }

    private function init()
    {
        $customer = EntityHelper::createCustomer();
        $customer->setFirstName('test');
        $customer->setLastName('customer');
        $customerName = $customer->getFirstName() . " " . $customer->getLastName();

        $orderWithoutName = EntityHelper::createOrder($customer);
        $orderWithoutName->setCustomerName(NULL);
        $orderWithoutName->setDtc(new Date('-15 days'));

        $orderWithName = EntityHelper::createOrder($customer);
        $orderWithName->setCustomerName($customerName);
        $orderWithName->setDtc(new Date('-15 days'));

        $order2 = clone $orderWithName;
        $order2->setDtc(new Date('-15 days'));
        $order3 = clone $orderWithName;
        $order3->setDtc(new Date('-15 days'));
        $order4 = clone $orderWithName;
        $order4->setDtc(new Date('-2 months'));

        $feefoWaiting = new Feefo();
        $feefoWaiting->setStatusId(Feefo::STATUS_WAITING);
        $feefoWaiting->setOrder($orderWithName);
        $feefoWaiting->setDtc(new Date('-15 days'));

        $feefoSent = new Feefo();
        $feefoSent->setStatusId(Feefo::STATUS_SENT);
        $feefoSent->setOrder($order2);
        $feefoSent->setDtc(new Date('-15 days'));

        $feefoEligibleWithData = new Feefo();
        $feefoEligibleWithData->setStatusId(Feefo::STATUS_ELIGIBLE);
        $feefoEligibleWithData->setOrder($order3);
        $feefoEligibleWithData->setDtc(new Date('-15 days'));

        $feefoEligibleWithDataTooOld = new Feefo();
        $feefoEligibleWithDataTooOld->setStatusId(Feefo::STATUS_ELIGIBLE);
        $feefoEligibleWithDataTooOld->setOrder($order4);
        $feefoEligibleWithDataTooOld->setDtc(new Date('-2 months'));

        $feefoEligibleWithoutData = new Feefo();
        $feefoEligibleWithoutData->setStatusId(Feefo::STATUS_ELIGIBLE);
        $feefoEligibleWithoutData->setOrder($orderWithoutName);
        $feefoEligibleWithoutData->setDtc(new Date('-15 days'));

        $orders = [$orderWithoutName, $orderWithName, $order2, $order3, $order4];
        $this->feefoLeads = [
            'waiting' => $feefoWaiting,
            'sent' => $feefoSent,
            'eligible' => $feefoEligibleWithData,
            'too_old' => $feefoEligibleWithDataTooOld,
            'no_data' => $feefoEligibleWithoutData
        ];
        $this->databaseHelper->saveEntity($customer);
        $this->databaseHelper->saveEntities($orders);
        $this->databaseHelper->saveEntities($this->feefoLeads);
    }

    private function clearTables()
    {
        $this->databaseHelper->emptyTables(EntityHelper::$tables);
    }
}
/*
 * Translated default messages for the jQuery validation plugin.
 * Locale: Az (Azeri; azərbaycan dili)
 */
$.extend( $.validator.messages, {
	required: "<PERSON>u xana mütləq doldurulmalıdır.",
	remote: "<PERSON>əhm<PERSON>t olmasa, düzgün məna daxil edin.",
	email: "<PERSON><PERSON><PERSON><PERSON><PERSON> olmasa, düzgün elektron poçt daxil edin.",
	url: "<PERSON>əhm<PERSON>t olmasa, düzgün URL daxil edin.",
	date: "Zəhm<PERSON>t olmasa, düzgün tarix daxil edin.",
	dateISO: "<PERSON><PERSON>hm<PERSON>t olmasa, düzgün ISO formatlı tarix daxil edin.",
	number: "<PERSON><PERSON>hm<PERSON>t olmasa, düzgün rəqəm daxil edin.",
	digits: "Zəhmət olmasa, yalnız rəqəm daxil edin.",
	creditcard: "<PERSON><PERSON><PERSON><PERSON><PERSON> olmasa, düzgün kredit kart nömrəsini daxil edin.",
	equalTo: "<PERSON><PERSON><PERSON><PERSON><PERSON> olma<PERSON>, e<PERSON>i mənanı bir daha daxil edin.",
	extension: "<PERSON><PERSON><PERSON><PERSON><PERSON> olmasa, düzgün geni<PERSON>lənməyə malik faylı seçin.",
	maxlength: $.validator.format( "Zəhmət olmasa, {0} simvoldan çox olmayaraq daxil edin." ),
	minlength: $.validator.format( "Zəhmət olmasa, {0} simvoldan az olmayaraq daxil edin." ),
	rangelength: $.validator.format( "Zəhmət olmasa, {0} - {1} aralığında uzunluğa malik simvol daxil edin." ),
	range: $.validator.format( "Zəhmət olmasa, {0} - {1} aralığında rəqəm daxil edin." ),
	max: $.validator.format( "Zəhmət olmasa, {0} və ondan kiçik rəqəm daxil edin." ),
	min: $.validator.format( "Zəhmət olmasa, {0} və ondan böyük rəqəm daxil edin" )
} );

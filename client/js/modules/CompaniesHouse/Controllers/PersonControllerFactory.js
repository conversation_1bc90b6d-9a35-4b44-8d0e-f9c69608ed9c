import { prefill } from './../Prefiller.js';
import { ServiceAddress } from '../Forms/ServiceAddress';
import { PersonAddress } from '../Forms/PersonAddress';
import { PersonController } from './PersonController';
import { IdCheckFormFactory } from '../../IdValidation/Forms/IdCheckFormFactory';
import { ResidentialAddressFactory } from '../Forms/ResidentialAddressFactory';
import { IdDataChecker } from '../Checkers/IdDataChecker';
import { IdStatus } from '../Entities/IdStatus';
import { CountryOfResidence } from '../Countries/CountryOfResidence';
import { PscController } from '../../Incorporation/PscController';
import { LegacyCountryOfResidence } from '../Countries/LegacyCountryOfResidence';
import { IdForm } from '../../IdValidation/IdForm';
import { idFormRules } from '../../IdValidation/Definitions/idFormDefinition';
import { PCAPredictController } from '../../PCAPredict/PCAPredictController';
import { Country } from '../Countries/Country';
import rivets from 'rivets';
import $ from 'jquery';
import { uiserver } from '../../external';
import { setPostcodeValidationRules } from '../../PCAPredict/GetAddress';

export class PersonControllerFactory {
  static bind (options, data, credasFeature) {
    const personController = PersonControllerFactory.create(options, data, credasFeature);

    rivets.bind($(options.rivets.selector), personController);

    // a hack for new pcsPredict lib which is used in a different smarty template to use this object
    window.personController = personController;

    $(options.tabs.selector).find('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
      personController.idForm.updateDocumentType(e, e.target);
    });

    uiserver.autofocus();

    if (options.idValidator) {
      $(options.rivets.selector).validate(
        idFormRules(
          options.rivets.selector,
          options.idValidator.fields,
          personController.residentialAddress.pca,
          options.idValidator.line1Url,
          options.idValidator.line2Url,
          options.idValidator.licenseUrl,
          personController.idForm,
          true
        )
      );

      // setting validation for new postcode lookup
      setPostcodeValidationRules('opc_input-residential');
      if ($('opc_input-service_address')) {
        setPostcodeValidationRules('opc_input-service_address');
      }
    }

    return personController;
  }

  static create (options, data, credasFeature) {
    const countryOfResidence = new CountryOfResidence(data.personAddress.countryOfResidence);
    const residentialAddress = ResidentialAddressFactory.create(data.residentialAddress, data.residentialAddress.pca, countryOfResidence);
    const idStatus = new IdStatus(data.idCheck.idStatus);
    const personAddress = new PersonAddress(countryOfResidence);
    const serviceAddressPca = data.serviceAddress.pca
      ? PCAPredictController.bind(data.serviceAddress.pca.key, data.serviceAddress.pca.data, data.serviceAddress.pca.context, Country.ukCountry().value)
      : null;
    const serviceAddress = new ServiceAddress(data.serviceAddress.isMsgAddress, countryOfResidence, serviceAddressPca);

    let pscController = null;
    if (options.psc && options.psc.prefix) {
      pscController = PscController.init('.nature-of-control-container', options.psc.prefix);
    }
    const idForm = new IdForm(
      data.idCheck.documentType,
      data.idCheck.passportCountry,
      data.idCheck.passportSecondNumber
    );

    const idDataChecker = new IdDataChecker(
      idStatus,
      data.idCheck.updateEnabled,
      data.idCheck.companyId,
      serviceAddress,
      residentialAddress,
      {
        personalData: data.idCheck.personalData,
        addressData: data.idCheck.addressData,
        residentialAddressData: data.idCheck.residentialAddressData
      },
      data.idCheck.type
    );

    const personController = new PersonController(
      personAddress,
      serviceAddress,
      residentialAddress,
      idForm,
      idDataChecker,
      pscController,
      credasFeature
    );

    const prefillSelectors = options.prefiller.selectors;
    const prefillData = options.prefiller.data;
    const prefillPrefixes = options.prefiller.prefixes;

    prefill($(prefillSelectors.officers), prefillPrefixes.officers, prefillData.officers, null, personController);
    prefill($(prefillSelectors.addresses), prefillPrefixes.addresses, prefillData.addresses, serviceAddress);

    return personController;
  }

  static createLegacy (options, data, credasFeature) {
    const countryOfResidence = new LegacyCountryOfResidence(data.personAddress.countryOfResidence);
    const residentialAddress = ResidentialAddressFactory.createLegacy(data.residentialAddress, data.residentialAddress.pca, countryOfResidence);
    const idStatus = new IdStatus(data.idCheck.idStatus);
    const serviceAddress = new ServiceAddress(data.serviceAddress.isMsgAddress, countryOfResidence);

    const idDataChecker = new IdDataChecker(
      idStatus,
      data.idCheck.updateEnabled,
      data.idCheck.companyId,
      residentialAddress,
      serviceAddress,
      {
        personalData: data.idCheck.personalData,
        addressData: data.idCheck.addressData,
        residentialAddressData: data.idCheck.residentialAddressData
      },
      data.idCheck.type
    );

    const personController = new PersonController(
      new PersonAddress(countryOfResidence),
      serviceAddress,
      residentialAddress,
      IdCheckFormFactory.create(options.tabs.selector, data.idCheck),
      idDataChecker,
      credasFeature
    );

    prefill($(options.prefiller.selectors.officers), null, options.prefiller.data.officers);
    prefill($(options.prefiller.selectors.addresses), null, options.prefiller.data.addresses);

    rivets.bind($(options.rivets.selector), personController);

    // a hack for new pcsPredict lib which is used in a different smarty template to use this object
    window.personController = personController;

    return personController;
  }
}

import { createFormElementsWithControl } from '../Factories/FormElementsWithControlFactory';
import $ from 'jquery';

export const ServiceResidentialAddressToggler = {
  bind (control, wrapper, residentialAddressControl, residentialAddressWrapper) {
    const serviceAddressElements = createFormElementsWithControl(control, wrapper, { invertedBehaviour: true, hasToCheckControl: true });
    const residentialAddressElements = createFormElementsWithControl(control, residentialAddressWrapper, { detachedWrapper: true });

    serviceAddressElements.toggleDisabled();

    $(control).change(() => {
      serviceAddressElements.toggleDisabled().removeError();
      residentialAddressElements.enable();
      if ($(control).prop('checked')) {
        $(residentialAddressControl).prop('checked', true);
      }
    });

    $('button[type=submit]').click(function () {
      residentialAddressElements.enable();
      if ($(control).prop('checked')) {
        $(residentialAddressControl).prop('checked', true);
      }
    });
  }
};

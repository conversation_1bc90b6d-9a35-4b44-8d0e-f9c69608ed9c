
export class ReasonList {
  constructor (reasonGroups) {
    this.checkGroups = reasonGroups.checkGroups;
    this.allGroups = reasonGroups.allGroups;
    this.defaultGroup = reasonGroups.defaultGroup;
  }

  hasCheck (checkName) {
    return checkName in this.checkGroups;
  }

  getCheckGroups (checkName) {
    return this.hasCheck(checkName) ? this.checkGroups[checkName] : [];
  }

  getDefaultGroup () {
    const group = {};
    group[this.defaultGroup] = this.getGroupReasons(this.defaultGroup);
    return group;
  }

  getGroupReasons (group) {
    return group in this.allGroups ? this.allGroups[group] : [];
  }

  getDefaultReasonKey () {
    for (var reasonKey in this.getGroupReasons(this.defaultGroup)) break;
    return reasonKey;
  }
}

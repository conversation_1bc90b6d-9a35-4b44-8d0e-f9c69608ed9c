import $ from 'jquery';

export function initLegacy (addresses, officers) {
  $(document).ready(function () {
    // start on beginning
    ourRegisterOfficeHandler();

    // called on type click
    $("input[name='ourServiceAddress']").click(function () {
      ourRegisterOfficeHandler();
    });

    /**
         * Provides disable and enable dropdown for our offices
         * @return void
         */
    function ourRegisterOfficeHandler () {
      var disabled = $('#ourServiceAddress').is(':checked');
      $('#prefillAddress').attr('disabled', disabled);
      $('#premise').attr('disabled', disabled);
      $('#street').attr('disabled', disabled);
      $('#thoroughfare').attr('disabled', disabled);
      $('#post_town').attr('disabled', disabled);
      $('#postcode').attr('disabled', disabled);
      $('#county').attr('disabled', disabled);
      $('#country').attr('disabled', disabled);
      if (disabled === true) {
        $('#service_address, #service_address_prefill, #ROMessage').hide();
        $('#addressSelectedMessage').show();
      } else {
        $('#service_address, #service_address_prefill, #ROMessage').show();
        $('#addressSelectedMessage').hide();
      }
    }

    // prefill address
    $('#prefillAddress').change(function () {
      var value = $(this).val();
      var address = addresses[value];
      for (var name in address) {
        $('#' + name).val(address[name]);
      }
    });

    // prefill officers
    $('#prefillOfficers').change(function () {
      var value = $(this).val();
      var address = officers[value];
      for (var name in address) {
        if (name === 'prescribed_particulars' || name === 'share_class' || name === 'num_shares' || name === 'share_value' || name === 'currency') {
          continue;
        }
        $('#' + name).val(address[name]);
      }
    });

    toogleServiceAddress();

    // service address
    $('#serviceAddress').click(function () {
      toogleServiceAddress();
    });

    function toogleServiceAddress () {
      var disabled = !$('#serviceAddress').is(':checked');
      $("input[id^='service_'], select[id^='service_']").each(function () {
        $(this).attr('disabled', disabled);
      });
    }

    // UK
    toogleUK();

    $("input[name='type']").click(function () {
      toogleUK();
    });

    function toogleUK () {
      // UK
      if ($('#type1').is(':checked')) {
        $('#place_registered').attr('disabled', false);
        $('#registration_number').attr('disabled', false);
        $('#law_governed').attr('disabled', true);
        $('#legal_form').attr('disabled', true);
        // Non UK
      } else if ($('#type2').is(':checked')) {
        $('#place_registered').attr('disabled', false);
        $('#registration_number').attr('disabled', false);
        $('#law_governed').attr('disabled', false);
        $('#legal_form').attr('disabled', false);
      } else {
        $('#place_registered').attr('disabled', true);
        $('#registration_number').attr('disabled', true);
        $('#law_governed').attr('disabled', true);
        $('#legal_form').attr('disabled', true);
      }
    }
  });
}

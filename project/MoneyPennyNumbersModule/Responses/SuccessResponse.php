<?php

namespace MoneyPennyNumbersModule\Responses;

use HttpClient\Requests\Request;
use MoneyPennyNumbersModule\Responses\Response;

class SuccessResponse extends Response
{
    public function __construct(Request $request, string $body = null, int $code = null, array $header = [])
    {
        $this->setHeader($header);
        $this->setBody($body);
        $this->setCode($code);
        $this->setRequest($request);
    }

    public function isSuccess(): bool
    {
        return true;
    }
}
<?php

namespace BankingModule\TideModule\Providers;

use BankingModule\Entities\CompanyCustomer;
use CompaniesHouseModule\Entities\DirectorPerson;
use CompaniesHouseModule\Entities\PscPerson;
use CompaniesHouseModule\Repositories\MemberRepository;

class CompanyDataProvider
{
    /**
     * @var DirectorsDataProvider
     */
    private $directorsDataProvider;

    /**
     * @var ShareholdersDataProvider
     */
    private $shareholdersDataProvider;

    /**
     * @var MemberRepository
     */
    private $memberRepository;

    public function __construct(
        DirectorsDataProvider $directorsDataProvider,
        ShareholdersDataProvider $shareholdersDataProvider,
        MemberRepository $memberRepository
    )
    {
        $this->directorsDataProvider = $directorsDataProvider;
        $this->shareholdersDataProvider = $shareholdersDataProvider;
        $this->memberRepository = $memberRepository;
    }

    public function getData(CompanyCustomer $companyCustomer)
    {
        $company = $companyCustomer->getCompany();

        $directors = $this->memberRepository->getCompanyMembersByEntityName($company, DirectorPerson::class);

        $companyData = [
            'name' => $company->getCompanyName(),
            'registered' => TRUE,
            'address' => $company->getRegisteredOffice()->toLines(),
            'number' => $company->getCompanyNumber(),
            'directors' => $this->directorsDataProvider->getData($directors)
        ];

        if ($company->isLimitedBySharesType()) {
            $pscs = $this->memberRepository->getCompanyMembersByEntityName($company, PscPerson::class);
            $companyData['shareholders'] = $this->shareholdersDataProvider->getData($pscs);
        }

        return $companyData;
    }
}

<?php

namespace BankingModule\Views;

use BankingModule\Entities\CompanyCustomer;
use BankingModule\Filters\BankOptionsFilter;
use Models\Products\BasketProduct;
use FrontModule\Controlers\CFRegisteredOfficeControler;
use FrontModule\Controlers\CFSummaryControler;
use Entities\Company;
use RouterModule\Generators\IUrlGenerator;
use RouterModule\Generators\UrlGenerator;
use Services\CashbackService;
use Services\NodeService;
use Utils\Helpers\ArrayHelper;

class ChooseBankViewFactory
{
    /**
     * @var BankOptionsFilter
     */
    private $bankOptionsFilter;

    /**
     * @var NodeService
     */
    private $nodeService;

    /**
     * @var UrlGenerator
     */
    private $urlGenerator;

    /**
     * @var CashbackService
     */
    private $cashbackService;

    /**
     * @var array
     */
    private static $templates = [
        CompanyCustomer::BANK_TYPE_BARCLAYS => 'barclays.tpl',
        CompanyCustomer::BANK_TYPE_CARD_ONE => 'cardOne.tpl',
        CompanyCustomer::BANK_TYPE_TIDE => 'tide.tpl',
        CompanyCustomer::BANK_TYPE_TSB => 'tsb.tpl',
        CompanyCustomer::BANK_TYPE_CASHPLUS => 'cashplus.tpl',
    ];

    public function __construct(
        BankOptionsFilter $bankOptionsFilter,
        NodeService $nodeService,
        UrlGenerator $urlGenerator,
        CashbackService $cashbackService
    ) {
        $this->bankOptionsFilter = $bankOptionsFilter;
        $this->nodeService = $nodeService;
        $this->urlGenerator = $urlGenerator;
        $this->cashbackService = $cashbackService;
    }

    public function create(Company $company, bool $canRedirectToSummary, string $selected = NULL): ChooseBankView
    {
        $package = $this->nodeService->getProductById($company->getProductId());

        return new ChooseBankView(
            $this->cashbackService->getCashBackAmountForCompany($company),
            $this->getOptions($company, $package, $selected),
            $this->getNoBankLink($company, $canRedirectToSummary)
        );
    }

    private function getOptions(Company $company, BasketProduct $package, string $selected = NULL): array
    {
        $options = [];

        foreach ($this->bankOptionsFilter->filter($company, $package->getBankingOptions()) as $name) {
            if ($template = ArrayHelper::get(self::$templates, $name, NULL)) {
                $options[$template] = ($name === $selected);
            }
        }

        return $options;
    }

    private function getNoBankLink(Company $company, bool $canRedirectToSummary): string
    {
        if ($canRedirectToSummary) {
            return $this->getSummaryPageLink($company);
        }

        return $this->getRegisteredOfficeLink($company);
    }

    private function getSummaryPageLink(Company $company): string
    {
        return $this->urlGenerator->url(
            'company_formation_module.incorporation_summary',
            ['company' => $company->getId()]
        );
    }

    private function getRegisteredOfficeLink(Company $company): string
    {
        return $this->urlGenerator->url(
            CFRegisteredOfficeControler::REGISTER_OFFICE_PAGE,
            [
                'company_id' => $company->getId(),
                'noBank' => 1,
            ],
            IUrlGenerator::OLD_LINK
        );
    }
}
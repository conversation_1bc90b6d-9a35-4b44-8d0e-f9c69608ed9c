<?php

namespace FeedbackModule\Commands;

use FeedbackModule\Dto\FeedbackServiceConfig;
use FeedbackModule\Exceptions\FeedbackUpdaterException;
use FeedbackModule\Updaters\FeedbackUpdater;
use Psr\Log\LoggerInterface;
use Utils\Exceptions\InvalidArgumentException;

class FeedbackUpdaterCommand
{
    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var FeedbackUpdater
     */
    private $updater;

    /**
     * @var array
     */
    private $servicesConfig;

    public function __construct(
        LoggerInterface $logger,
        FeedbackUpdater $updater,
        array $servicesConfig
    ) {
        $this->logger = $logger;
        $this->updater = $updater;
        $this->servicesConfig = $servicesConfig;
    }

    public function update(): void
    {
        $feedbacks = [];
        foreach ($this->servicesConfig as $service => $config) {
            try {
                $feedback = $this->updater->update(FeedbackServiceConfig::fromArray($config));
                $feedbacks[$service] = $feedback->toArray();
            } catch (InvalidArgumentException $e) {
                $this->logger->error(sprintf('Update failed (%s).', $service), ['e' => $e]);
            } catch (FeedbackUpdaterException $e) {
                $this->logger->error(sprintf('Update failed (%s).', $service), ['e' => $e]);
            }
        }
        $this->logger->debug('Feedback data updated.', ['data' => $feedbacks]);
    }
}

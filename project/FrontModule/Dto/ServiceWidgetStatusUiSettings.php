<?php

namespace FrontModule\Dto;

use Utils\Helpers\ArrayHelper;

class ServiceWidgetStatusUiSettings
{
    /**
     * @var string
     */
    private $statusTextColor;

    /**
     * @var string
     */
    private $statusIconTag;

    /**
     * @var bool
     */
    private $showStatusInMultipleLine;

    /**
     * @var string|null
     */
    private $loadFromTemplate;

    /**
     * @var bool|null
     */
    private $addSpaceBetweenLines;

    /**
     * @var string|null
     */
    private $overrideTextDescription;

    public function __construct(string $statusTextColor, string $statusIconTag, bool $showStatusInMultipleLine, ?string $loadFromTemplate, bool $addSpaceBetweenLines, $overrideTextDescription)
    {
        $this->statusTextColor = $statusTextColor;
        $this->statusIconTag = $statusIconTag;
        $this->showStatusInMultipleLine = $showStatusInMultipleLine;
        $this->loadFromTemplate = $loadFromTemplate;
        $this->addSpaceBetweenLines = $addSpaceBetweenLines;
        $this->overrideTextDescription = $overrideTextDescription;
    }

    public static function fromArray(array $data): self
    {
        return new self(
            ArrayHelper::get($data, 'statusTextColor', 'grey'),
            ArrayHelper::get($data, 'statusIconTag', '<i class="fa fa-minus fa-fw grey"></i>'),
            ArrayHelper::get($data, 'showStatusInMultipleLine', false),
            ArrayHelper::get($data, 'loadFromTemplate', null),
            ArrayHelper::get($data, 'addSpaceBetweenLines', true),
            ArrayHelper::get($data, 'overrideTextDescription', null)
        );
    }

    public function getStatusTextColor(): string
    {
        return $this->statusTextColor;
    }

    public function getStatusIconTag(): string
    {
        return $this->statusIconTag;
    }

    public function showStatusInMultipleLine(): string
    {
        return $this->showStatusInMultipleLine;
    }

    public function loadFromTemplate(): ?string
    {
        return $this->loadFromTemplate ? sprintf('./%s', $this->loadFromTemplate) : null;
    }

    public function addSpaceBetweenLines(): ?bool
    {
        return $this->addSpaceBetweenLines;
    }

    public function getOverrideTextDescription(): ?string
    {
        return $this->overrideTextDescription;
    }

    public function shouldOverrideTextDescription(): bool
    {
        return !empty($this->overrideTextDescription);
    }

}

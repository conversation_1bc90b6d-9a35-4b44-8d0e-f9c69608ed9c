{include file="@header.tpl"}

{* JS FOR PREFILL *}
<script type="text/javascript">
/* <![CDATA[ */
var addresses = {$jsPrefillAdresses nofilter};
var officers = {$jsPrefillOfficers nofilter};
/* ]]> */
</script>

{literal}
<script>
$(document).ready(function () {

	// prefill address	
	$("#prefillAddress").change(function () {
		var value = $(this).val();
		address = addresses[value];
		for (var name in address) {
			$('#'+name).val(address[name]);
		}
	});
	
	// prefill officers	
	$("#prefillOfficers").change(function () {
		var value = $(this).val();
		address = officers[value];
		for (var name in address) {
			$('#'+name).val(address[name]);
		}
	});

	toogleServiceAddress();
	
	// service address
	$("#serviceAddress").click(function (){
		toogleServiceAddress();
	});
	
	function toogleServiceAddress() {
		disabled = !$("#serviceAddress").is(":checked");
		$("input[id^='service_'], select[id^='service_']").each(function () {
			$(this).attr("disabled", disabled);
		}); 
	}
	
});
</script>
{/literal}

<div class="row">
	<div class="col-xs-12">
		<p style="margin: 0 0 15px 0; font-size: 11px;">
			<a href="{$this->router->link("FrontModule\controlers\CUSummaryControler::SUMMARY_PAGE", "company_id=$companyId")}">
				{$companyName}
			</a>
			&gt; {$title}
		</p>
	</div>

	{if $visibleTitle}
    <div class="col-xs-12">
		<h1>{$title}</h1>
	</div>
    {/if}

	<div class="col-xs-12">
		<p style="text-align: right;">To make a corporate appointment <a href="{$this->router->link("FrontModule\controlers\CUSecretariesControler::ADD_SECRETARY_CORPORATE_PAGE", "company_id=$companyId")}">click here</a></p>
	</div>

	<div class="col-xs-12" style="background-color: #f2f3f5; padding-top: 10px; padding-bottom:10px;">
		{$form nofilter}
	</div>
</div>
{include file="@footer.tpl"}

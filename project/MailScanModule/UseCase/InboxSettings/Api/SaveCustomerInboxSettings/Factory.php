<?php

declare(strict_types=1);

namespace MailScanModule\UseCase\InboxSettings\Api\SaveCustomerInboxSettings;

use Entities\Customer;
use MailScanModule\Validators\SettingsValidator;

readonly class Factory
{
    public function makeRequest(
        Customer $customer,
        array $payload,
    ): Request {
        SettingsValidator::validateRequiredKeys($payload, SettingsValidator::REQUIRED_CUSTOMER_SETTINGS_KEYS);

        return new Request(
            $customer,
            $payload
        );
    }

    public function makeResponse(): Response
    {
        return new Response();
    }
}

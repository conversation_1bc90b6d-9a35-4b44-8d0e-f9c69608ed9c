<?php

declare(strict_types=1);

namespace MailScanModule\Listeners;

use Config\Constants\EventLocator;
use Dispatcher\Events\OrderEvent;
use MailScanModule\Services\PostItemService;
use Models\Products\Product;
use Services\NodeService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

readonly class MailboxUpgradeListener implements EventSubscriberInterface
{
    public function __construct(
        private NodeService $nodeService,
        private PostItemService $postItemService,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            EventLocator::ORDER_COMPLETED => 'onOrderCompleted',
        ];
    }

    public function onOrderCompleted(OrderEvent $orderEvent): void
    {
        try {
            $order = $orderEvent->getOrder();
            foreach ($order->getItems() as $item) {
                foreach (Product::MAILBOX_PRODUCTS_FROM_UPGRADE as $productName) {
                    $product = $this->nodeService->requiredProductByName($productName);
                    if ($item->getProductId() === $product->getId()) {
                        $this->postItemService->setPostItemsToReprocess($item->getCompany());
                    }
                }
            }
        } catch (\Throwable $e) {
            throw new \Exception(sprintf('Failed to set post items to reprocess on mailbox upgrade: %s', $e->getMessage()));
        }
    }
}

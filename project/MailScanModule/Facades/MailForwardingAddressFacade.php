<?php

declare(strict_types=1);

namespace MailScanModule\Facades;

use CompaniesHouseModule\Entities\DirectorPerson;
use CompaniesHouseModule\Entities\Member;
use CompaniesHouseModule\Repositories\MemberRepository as CHMemberRepository;
use CompanyModule\Entities\Settings\MailForwardingAddressSetting;
use CompanyModule\Repositories\CompanySettingsRepository;
use Doctrine\ORM\NonUniqueResultException;
use Entities\Company;
use MailScanModule\Exceptions\InvalidForwardingAddressException;
use Psr\Log\LoggerInterface;

class MailForwardingAddressFacade
{
    public function __construct(
        private CompanySettingsRepository $companySettingsRepository,
        private CHMemberRepository $memberRepository,
        private LoggerInterface $logger,
    ) {
    }

    /**
     * @throws NonUniqueResultException
     * @throws InvalidForwardingAddressException
     */
    public function getForwardingAddress(Company $company): array
    {
        return $this->getMailForwardingAddressSetting($company)->toArray();
    }

    /**
     * @throws NonUniqueResultException
     * @throws InvalidForwardingAddressException
     */
    public function setForwardingAddress(Company $company, array $address): void
    {
        if (
            empty($address['address1'])
            || empty($address['address2'])
            || empty($address['town'])
            || empty($address['country'])
            || empty($address['postcode'])
        ) {
            throw new \InvalidArgumentException('You must provide a valid forwarding address that contains an Building name/number, Street, Town, Country and Postcode.');
        }

        $setting = $this->getMailForwardingAddressSetting($company);

        $setting->setAddress1($address['address1']);
        $setting->setAddress2($address['address2']);
        $setting->setAddress3($address['address3']);
        $setting->setTown($address['town']);
        $setting->setCountry($address['country']);
        $setting->setPostcode($address['postcode']);

        if (!$setting->isValid()) {
            throw new \Exception("You must provide a valid forwarding address that contains an Building name/number, Street, Town, Country, Postcode, and can't be our Registered Office address. Please check the spelling of the address.");
        }

        $this->persistSetting($setting);
    }

    /**
     * @throws InvalidForwardingAddressException
     */
    public function setSettingsAsFirstDirectorAddress(Company $company, MailForwardingAddressSetting $setting): MailForwardingAddressSetting
    {
        /** @var Member[] $directors */
        $directors = $this->memberRepository->getCompanyMembersByEntityName($company, DirectorPerson::class);

        if (empty($directors)) {
            return $setting->reset();
        }

        /** @var DirectorPerson $director */
        $director = $directors[0];
        $address = $director->getResidentialAddress();

        if (!$address->hasMainFields()) {
            $address = $director->getAddress();
        }

        $setting->setAddress1($address->getPremise())
            ->setAddress2($address->getStreet())
            ->setAddress3($address->getThoroughfare())
            ->setTown($address->getPostTown())
            ->setCountry($address->getCountry())
            ->setPostcode($address->getPostcode());

        if (!$setting->isValid()) {
            return $setting->reset();
        }

        return $setting;
    }

    /**
     * @throws InvalidForwardingAddressException
     * @throws NonUniqueResultException|\Exception
     */
    public function getMailForwardingAddressSetting(Company $company): MailForwardingAddressSetting
    {
        try {
            /** @var MailForwardingAddressSetting|null $setting */
            $setting = $this->companySettingsRepository->getSettingByClass($company, MailForwardingAddressSetting::class);

            if (is_null($setting) || !$setting->isValid()) {
                $setting = $this->setSettingsAsFirstDirectorAddress($company, $setting ?? new MailForwardingAddressSetting($company));
            }
        } catch (NonUniqueResultException $e) {
            throw new \Exception(sprintf(
                'This company (ID: %s) has more than one mail forwarding address setting.',
                $company->getId()
            ));
        } catch (\Throwable $e) {
            $this->logger->error('An unexpected error occurred while retrieving the mail forwarding address setting.', ['exception' => $e]);
            $setting = new MailForwardingAddressSetting($company);
        }

        return $setting;
    }

    public function persistSetting(MailForwardingAddressSetting $setting): void
    {
        $this->companySettingsRepository->persist($setting);
        $this->companySettingsRepository->flush();
    }

    public function clearDoctrineMemory()
    {
        $this->companySettingsRepository->getEntityManager()->clear();
    }
}

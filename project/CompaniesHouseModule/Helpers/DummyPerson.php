<?php

namespace CompaniesHouseModule\Helpers;

use CompaniesHouseModule\Entities\Address;
use CompaniesHouseModule\Entities\PersonalDetails;
use Faker\Factory;
use Utils\Date;

class DummyPerson
{
    /**
     * @var PersonalDetails
     */
    private $personalDetails;

    /**
     * @var Address
     */
    private $address;

    /**
     * @var Address
     */
    private $residentialAddress;

    public function __construct()
    {
        $faker = Factory::create('en_GB');

        $this->personalDetails = new PersonalDetails(
            $faker->firstName,
            $faker->lastName,
            Date::createFromDateTime($faker->dateTimeThisDecade),
            'British',
            $faker->country,
            'Officer',
            '',
            'Mr'
        );

        $this->address = new Address(
            $faker->buildingNumber,
            $faker->streetName,
            $faker->city,
            $faker->postcode,
            $faker->country
        );

        $this->residentialAddress = new Address(
            $faker->buildingNumber,
            $faker->streetName,
            $faker->city,
            $faker->postcode,
            $faker->country
        );
    }

    public function getPersonalDetails(): PersonalDetails
    {
        return $this->personalDetails;
    }

    public function getAddress(): Address
    {
        return $this->address;
    }

    public function getResidentialAddress(): Address
    {
        return $this->residentialAddress;
    }
}
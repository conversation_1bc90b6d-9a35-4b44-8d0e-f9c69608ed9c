<?php

namespace Repositories;

use Doctrine\ORM\Internal\Hydration\IterableResult;
use Entities\EntityAbstract;
use OrmModule\Contracts\IRepository;
use OrmModule\Repositories\DoctrineRepository_Abstract;

/**
 * @deprecated use DoctrineRepository_Abstract
 */
abstract class BaseRepository_Abstract extends DoctrineRepository_Abstract implements IRepository
{
    /**
     * @param int $entityId
     */
    public function getEntityById(int $entityId)
    {
        $entity = $this->find($entityId);
        return $entity;
    }

    public function getAllEntities(): array
    {
        $entities = $this->findAll();
        return $entities;
    }

    /**
     * @param mixed $entity
     * @return mixed
     */
    public function saveEntity($entity): mixed
    {
        $this->_em->persist($entity);
        $this->_em->flush();
        return $entity;
    }

    /**
     * @return void
     */
    public function removeEntity($entity): void
    {
        $this->_em->remove($entity);
        $this->_em->flush();
    }

    /**
     * @param array $entities
     * @return void
     */
    public function removeEntities(array $entities): void
    {
        foreach ($entities as $entity) {
            $this->_em->remove($entity);
        }

        $this->_em->flush();
    }

    /**
     * persist entity
     * @param EntityAbstract $entity
     */
    public function persist($entity)
    {
        $this->_em->persist($entity);
    }

    /**
     * Provides flush
     *
     * @param NULL|object|array $entity
     */
    public function flush($entity = NULL)
    {
        $this->_em->flush($entity);
    }

    /**
     * Returns key and value pairs as one array
     *
     * @return array $arr
     */
    public function getPairs($key, $value)
    {
        $iterate = $this->findAll();
        $arr = [];
        foreach ($iterate as $entity) {
            $arr[$entity->$key] = $entity->$value;
        }
        return $arr;
    }

    /**
     * count all records in the database
     */
    public function countAll()
    {
        $meta = $this->getClassMetadata();
        $prKeys = $meta->identifier;
        $arr = [];
        foreach ($prKeys as $pr) {
            $arr[] = 'c.' . $pr;
        }
        $select = 'COUNT(' . implode(',', $arr) . ')';
        $qb = $this->_em->createQueryBuilder();
        $qb->select($select);
        $qb->from($this->_entityName, 'c');
        return $qb->getQuery()->getSingleScalarResult();
    }

    public function in(string $name, array $ids)
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('c')->from($this->_entityName, 'c');
        $qb->add('where', $qb->expr()->in('c.' . $name, $ids));
        return $qb->getQuery()->getResult();
    }

    /**
     * get iterator to all existing entities
     * @return IterableResult
     */
    public function findAllIterator(): IterableResult
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('entity')->from($this->_entityName, 'entity');
        $query = $qb->getQuery();
        return $query->iterate();
    }

    /**
     * get iterator to all entities in a table
     * @param string $name
     * @param array $ids
     * @return IterableResult
     */
    public function findByIdsIterator(string $name, array $ids): IterableResult
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('entity')->from($this->_entityName, 'entity');
        $qb->add('where', $qb->expr()->in('entity.' . $name, $ids));
        $query = $qb->getQuery();
        return $query->iterate();
    }

}

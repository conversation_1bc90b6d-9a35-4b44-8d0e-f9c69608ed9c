<?php

namespace FraudProtectionModule\Commands;

use Cron\Commands\CommandAbstract;
use Cron\Commands\ICommand;
use <PERSON>ron\INotifier;
use DateTime;
use FraudProtectionModule\Exporters\FraudProtectionCsvExporter;
use FraudProtectionModule\Services\FraudProtectionQueueService;
use FraudProtectionModule\Uploaders\FraudProtectionFtpUploader;
use Psr\Log\LoggerInterface;

/**
 * @deprecated There is a new Fraud Protection service that is now sold instead of this one.
 * The new Fraud Protection is handle by CMS, so this command is no longer needed.
 * @description Exports CSV with fraud protection companies
 */
class ProcessFraudProtectionCompaniesCommand
{
    /**
     * @var string
     */
    protected $timeType = CommandAbstract::TIME_TYPE_FIXED;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @var INotifier
     */
    protected $notifier;

    /**
     * @var FraudProtectionQueueService
     */
    private $service;

    /**
     * @var FraudProtectionCsvExporter
     */
    private $exporter;
    
    public function __construct(
        LoggerInterface $logger,
        INotifier $notifier,
        FraudProtectionQueueService $service,
        FraudProtectionCsvExporter $exporter
    )
    {
        $this->logger = $logger;
        $this->notifier = $notifier;
        $this->service = $service;
        $this->exporter = $exporter;
    }

    public function execute()
    {
        $this->processFraudProtections();
        $this->logger->info('CSV with fraud protected companies has been uploaded.');

        $date = new DateTime;
        $this->notifier->triggerSuccess(
            $this->getName(),
            $date->getTimestamp(),
            "CSV with fraud protected companies has been uploaded. Date {$date->format('d/m/Y')}"
        );
    }

    private function processFraudProtections()
    {
        $queueItems = $this->service->getQueueItemsToSend();

        $file = $this->exporter->exportCompanies($queueItems);
        $filename = date('dmy') . '.csv';
        $file->move('/data/fraud_protection/', $filename);

        foreach ($queueItems as $queueItem) {
            $this->service->removeFromQueue($queueItem);
        }
    }

    private function getName(): string
    {
        $name = get_class($this);
        $name = strtolower($name);
        return str_replace('\\', '.', $name);
    }
}

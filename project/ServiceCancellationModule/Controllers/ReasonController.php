<?php

namespace ServiceCancellationModule\Controllers;

use Entities\Service;
use FrontModule\controlers\MyServicesControler;
use Repositories\ServiceSettingsRepository;
use RouterModule\Helpers\IControllerHelper;
use ServiceCancellationModule\Dto\ReasonData;
use ServiceCancellationModule\Factories\ReasonFactory;
use ServiceCancellationModule\Forms\ReasonForm;
use ServiceCancellationModule\Providers\ReasonProvider;
use ServiceCancellationModule\Repositories\ReasonDataRepository;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use TemplateModule\Renderers\IRenderer;

class ReasonController
{
    /**
     * @var IRenderer
     */
    private $renderer;

    /**
     * @var IControllerHelper
     */
    private $controllerHelper;

    /**
     * @var ReasonProvider
     */
    private $reasonProvider;

    /**
     * @var ReasonDataRepository
     */
    private $reasonDataRepository;

    public function __construct(
        IRenderer $renderer,
        IControllerHelper $controllerHelper,
        ReasonProvider $reasonProvider,
        ReasonDataRepository $reasonDataRepository
    ) {
        $this->renderer = $renderer;
        $this->controllerHelper = $controllerHelper;
        $this->reasonProvider = $reasonProvider;
        $this->reasonDataRepository = $reasonDataRepository;
    }
    
    public function choose(Service $service): Response
    {
        $company = $service->getCompany();
        $customer = $company->getCustomer();
        $reasons = $this->reasonProvider->getReasons($service);
        $data = new ReasonData();
        
        if ($redisData = $this->reasonDataRepository->optional($service)) {
            $data = $redisData;
        }
        
        $form = $this->controllerHelper->buildForm(ReasonForm::class, $data, ['reasons' => $reasons]);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $this->reasonDataRepository->saveEntity($service, $data);
            
            return new RedirectResponse(
                $this->controllerHelper->url(
                    'service_cancel_requirements_check', 
                    ['service' => $service->getId()]
                )
            );
        }
        
        return $this->renderer->render([
            'form' => $form->createView(),
            'dataReason' => $data->getReason(),
            'service' => $service,
            'company' => $company,
            'customer' => $customer,
            'seo' => ['title' => 'Service Cancellation'],
            'servicesPage' => MyServicesControler::PAGE_SERVICES
        ]);             
    }
}
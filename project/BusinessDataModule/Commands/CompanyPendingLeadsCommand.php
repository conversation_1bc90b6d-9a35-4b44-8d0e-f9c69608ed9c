<?php

namespace BusinessDataModule\Commands;

use BankingModule\Repositories\BankingRepository;
use BusinessDataModule\Deciders\RemindersDecider;
use BusinessDataModule\Emailers\BdgEmailer;
use BusinessDataModule\Repositories\BdgRepository;
use BusinessDataModule\Repositories\LeadDataRemover;
use BusinessServicesModule\Entities\Events\EmailEvent;
use BusinessServicesModule\Entities\Lead as BusinessLead;
use BusinessServicesModule\Repositories\LeadRepository;
use Doctrine\ORM\NonUniqueResultException;
use EmailModule\IEmailLog;
use Models\Products\Package;
use Psr\Log\LoggerInterface;

/**
 * @description Sends reminder emails to incorporated companies with pending BDG leads
 */
class CompanyPendingLeadsCommand
{

    /**
     * @var BdgRepository
     */
    private $bdgRepository;

    /**
     * @var LeadRepository
     */
    private $leadRepository;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var BankingRepository
     */
    private $bankingRepository;

    /**
     * @var RemindersDecider
     */
    private $decider;

    /**
     * @var BdgEmailer
     */
    private $emailer;

    /**
     * @var LeadDataRemover
     */
    private $leadDataRemover;

    public function __construct(
        BdgRepository $bdgRepository,
        LeadRepository $leadRepository,
        LoggerInterface $logger,
        BankingRepository $bankingRepository,
        RemindersDecider $decider,
        BdgEmailer $emailer,
        LeadDataRemover $leadDataRemover
    ) {
        $this->bdgRepository = $bdgRepository;
        $this->leadRepository = $leadRepository;
        $this->logger = $logger;
        $this->bankingRepository = $bankingRepository;
        $this->decider = $decider;
        $this->emailer = $emailer;
        $this->leadDataRemover = $leadDataRemover;
    }

    /**
     * @throws NonUniqueResultException
     */
    public function process(bool $dryRun = false): void
    {
        $counter = 0;
        $leads = $this->bdgRepository->getPendingLeadsForIncorporatedCompaniesExcludingProduct(Package::PACKAGE_ANNA);

        foreach ($leads as $lead) {

            $businessLead = $lead->getBusinessLead();
            $days = $lead->getDaysOld();

            switch (true) {
                case in_array($days, range(1, 2)):
                    $this->processReminder($businessLead, BdgEmailer::TAG_FIRST_REMINDER, $dryRun, $counter);
                    break;
                case in_array($days, range(3, 6)):
                    $this->processReminder($businessLead, BdgEmailer::TAG_SECOND_REMINDER, $dryRun, $counter);
                    break;
                case in_array($days, range(7, 9)):
                    $this->processReminder($businessLead, BdgEmailer::TAG_THIRD_REMINDER, $dryRun, $counter);
                    break;
                case $days >= 10:
                    $this->deleteLeadData($businessLead, $dryRun);
                    break;
            }
        }
    }

    /**
     * @throws NonUniqueResultException
     */
    private function processReminder(BusinessLead $businessLead, string $eventKey, bool $dryRun, int &$counter): void
    {
        if ($this->decider->canSendReminder($businessLead, $eventKey)) {
            $log = null;
            if (!$dryRun) {
                $log = $this->emailer->sendReminder($businessLead, $eventKey);
                $this->leadRepository->saveEvent(new EmailEvent($businessLead, $log, $eventKey));
            }
            $counter++;
            $this->logReminderProcessed($eventKey, $businessLead, $log);
        }
    }

    private function deleteLeadData(BusinessLead $businessLead, bool $dryRun)
    {
        $businessLeadId = $businessLead->getId();

        if (!$dryRun) {
            $this->leadDataRemover->removeData($businessLead);
        }

        $this->logger->debug(
            'Lead has been deleted.',
            [
                'lead_id' => $businessLeadId,
                'company_id' => $businessLead->getCompany()->getId()
            ]
        );
    }

    private function logReminderProcessed(string $eventKey, BusinessLead $lead, IEmailLog $log = null)
    {
        $this->logger->debug(
            'Reminder processed successfully',
            [
                'event' => $eventKey,
                'lead' => $lead->getId(),
                'company_id' => $lead->getCompany()->getId(),
                'emailLog' => $log ? $log->getId() : null,
            ]
        );
    }
}
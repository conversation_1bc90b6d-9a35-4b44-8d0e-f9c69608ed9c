{extends 'base.tpl'}

{block content}
    {styles file='webloader_front.neon' section='formation'}

    <div id="app" class="container custom-container pb-5 mt-4">
        <memorandum-and-articles-step
            company="{$company}"
            tab-steps="{$tabSteps}"
            :serialized-current-step="{json_encode($serializedCurrentStep)}"
            back-url="{$backUrl}"
            v-bind:has-custom-articles="{$hasCustomArticles}"
            v-bind:has-multiple-share-classes="{$hasMultipleShareClasses}"
            reserved-words="{$reservedWords}"
            upload-article-endpoint="{$uploadArticleEndpoint}"
            remove-article-endpoint="{$removeArticleEndpoint}"
            download-article-endpoint="{$downloadArticleEndpoint}"
            custom-memorandum-and-articles="{$customMemorandumAndArticles}"
            :memorandum-and-articles="{$memorandumAndArticles}"
            supporting-document="{$supportingDocument}"
            max-file-size="{$maxFileSize}"
            max-supporting-documents="{$maxSupportingDocuments}"
            change-name-url="{$changeNameUrl}"
        >
        </memorandum-and-articles-step>
    </div>
{/block}
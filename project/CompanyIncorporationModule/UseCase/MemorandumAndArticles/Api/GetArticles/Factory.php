<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\UseCase\MemorandumAndArticles\Api\GetArticles;

use CompanyIncorporationModule\Dto\MemorandumAndArticlesRenderData;
use Entities\Company;

class Factory
{
    public function makeRequest(Company $company): Request
    {
        return new Request(
            $company,
        );
    }

    public function makeResponse(MemorandumAndArticlesRenderData $memorandumAndArticlesRenderData): Response
    {
        return new Response(
            $memorandumAndArticlesRenderData->getMemorandumAndArticles(),
            $memorandumAndArticlesRenderData->getSupportingDocument(),
        );
    }
}

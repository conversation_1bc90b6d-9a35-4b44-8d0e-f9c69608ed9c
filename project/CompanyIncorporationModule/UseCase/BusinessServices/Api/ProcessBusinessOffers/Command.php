<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\UseCase\BusinessServices\Api\ProcessBusinessOffers;

use BusinessServicesModule\Facades\SelectOffersFacade;
use Services\EventService;

class Command
{
    public const EVENT_NAME = 'company_formation.business_offers_process';
    public const EVENT_NAME_SKIPPED = 'company_formation.business_offers_skipped';
    public const EVENT_TOOLKIT_NAME = 'company_formation.toolkit_offers_process';
    public const EVENT_TOOLKIT_NAME_SKIPPED = 'company_formation.toolkit_offers_skipped';

    public function __construct(
        private Factory $factory,
        private SelectOffersFacade $selectOffersFacade,
        private EventService $eventService,
    ) {
    }

    public function execute(Request $request): Response
    {
        $this->notify($request);

        $this->selectOffersFacade->setAllNonBankingUnprocessedLeadsAsDeleted($request->company);

        $offers = $this->mapOffers($request->offers);

        foreach ($offers as $categoryId => $selectedOfferIds) {
            $this->selectOffersFacade->processOffers(
                $request->company,
                $selectedOfferIds,
                $categoryId
            );
        }

        $this->selectOffersFacade->setDuplicateLeadsAsDeleted($request->company);

        return $this->factory->makeResponse();
    }

    private function notify(Request $request): void
    {
        $this->eventService->notifyPreventDuplicationCached(
            empty($request->offers) ? self::EVENT_NAME_SKIPPED : self::EVENT_NAME,
            $request->company->getId()
        );
        $this->eventService->notifyPreventDuplicationCached(
            empty($request->toolkitOffers) ? self::EVENT_TOOLKIT_NAME_SKIPPED : self::EVENT_TOOLKIT_NAME,
            $request->company->getId()
        );
    }

    private function mapOffers(array $offers): array
    {
        $mappedOffers = [];

        foreach ($offers as $item) {
            $categoryId = $item['categoryId'];
            $selectedOfferId = $item['selectedOfferId'];

            if (!isset($mappedOffers[$categoryId])) {
                $mappedOffers[$categoryId] = [];
            }

            $mappedOffers[$categoryId][] = $selectedOfferId;
        }

        return $mappedOffers;
    }
}

<?php

namespace PaymentModule\Loggers;

use Entities\Transaction;
use Exception;
use OrmModule\Repositories\IPersistRepository;
use PaymentModule\Contracts\IPaymentData;
use PaymentModule\Contracts\IPaymentResponse;
use SagePayToken\Token\Exception\FailedResponse;
use UserModule\Contracts\ICustomer;
use Utils\Exceptions\InvalidDateException;

final class TransactionPaymentLogger implements IPaymentLogger
{
    /**
     * @var IPersistRepository
     */
    private $repository;

    /**
     * @param IPersistRepository $repository
     */
    public function __construct(IPersistRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * @param ICustomer $customer
     * @param IPaymentData $paymentData
     * @param IPaymentResponse $paymentResponse
     * @return Transaction
     */
    public function createSuccessTransaction(ICustomer $customer, IPaymentData $paymentData, IPaymentResponse $paymentResponse)
    {
        $transaction = Transaction::fromPaymentResponse($customer, $paymentResponse);
        $transaction->setStatusId(Transaction::STATUS_SUCCEEDED);
        if ($token = $paymentData->getToken()) {
            $transaction->setToken($token);
        }
        return $transaction;
    }

    /**
     * @param Exception $e
     * @param ICustomer $customer
     * @param IPaymentData $paymentData
     * @return Transaction
     * @throws InvalidDateException
     */
    public function createFailedTransaction(Exception $e, ICustomer $customer, IPaymentData $paymentData)
    {
        $transaction = Transaction::fromPaymentData($customer, $paymentData);
        $transaction->setStatusId(Transaction::STATUS_FAILED);
        $transaction->setError($e->getMessage());
        $paymentDataToUse = $e instanceof FailedResponse && $e->getPaymentData() ? $e->getPaymentData() : $paymentData;
        $transactionSummary = $e instanceof FailedResponse && $e->getSummary() ? $e->getSummary() : null;

//        if ($paymentDataToUse->isUsingToken() && $token = $paymentDataToUse->getToken()) {
//            $transaction->setToken($token);
//        }
        if ($transactionSummary) {
            $transaction->setTransactionIds($transactionSummary);
        }
        if (!$transaction->hasCardDetails() && $paymentDataToUse->getCardDetails()) {
            $transaction->setCardDetails($paymentDataToUse->getCardDetails());
        }
        return $transaction;
    }

    /**
     * @param ICustomer $customer
     * @param IPaymentData $paymentData
     * @param IPaymentResponse $paymentResponse
     * @return Transaction
     */
    public function success(ICustomer $customer, IPaymentData $paymentData, IPaymentResponse $paymentResponse)
    {
        $transaction = $this->createSuccessTransaction($customer, $paymentData, $paymentResponse);
        return $this->repository->saveEntity($transaction);
    }

    /**
     * @param Exception $e
     * @param ICustomer $customer
     * @param IPaymentData $paymentData
     * @param string|NULL $message
     * @return Transaction|null
     */
    public function error(Exception $e, ICustomer $customer, IPaymentData $paymentData, $message = NULL)
    {
        if (!$customer->getId()) {
            return;
        }
        $transaction = $this->createFailedTransaction($e, $customer, $paymentData);
        return $this->repository->saveEntity($transaction);
    }
}
<?php

namespace TemplatingModule;

use Li<PERSON>\Basket;
use GhostModule\Services\GhostService;
use IdModule\Providers\CredasValidationStatusProvider;
use Libs\Helpers\Currency;
use NotificationModule\Services\NotificationService;
use Smarty\Smarty;
use Smarty\Template;
use TemplatingModule\Generators\PositionGenerator;
use CommonModule\UrlGenerator;
use TemplatingModule\Services\TemplateComponentService;
use UserModule\Services\CustomerAvailability;
use const DIRECTORY_SEPARATOR;
use const DOCUMENT_ROOT;
use Exception;
use Framework\FApplication;
use FeatureModule\Feature;
use Framework\FTools;
use IdModule\Factories\DiligenceLevelFactory;
use IdModule\Views\ReasonProvider;
use InvalidArgumentException;
use League\CommonMark\CommonMarkConverter;
use PeopleWithSignificantControlModule\Providers\NatureOfControlTextProvider;
use RouterModule\Helpers\IControllerHelper;
use Services\NodeService;
use Symfony\Component\DependencyInjection\Container;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Utils\CallbackDefinition;
use Utils\Helpers\ArrayHelper;
use Utils\Helpers\DateHelper;
use Utils\Helpers\StringHelper;
use WebloaderHelper\Config as WebloaderConfig;
use WebloaderHelper\WebloaderHelper;

class SmartySingletonFactory
{
    /**
     * @var Smarty
     */
    private static $smarty;

    /**
     * @var UrlGenerator
     */
    private $urlGenerator;

    /**
     * @var NodeService
     */
    private $nodeService;

    /**
     * @var WebloaderConfig
     */
    private $config;

    /**
     * @var CommonMarkConverter
     */
    private $converter;

    /**
     * @var DiligenceLevelFactory
     */
    private $diligenceLevelFactory;

    /**
     * @var ReasonProvider
     */
    private $reasonProvider;

    /**
     * @var IControllerHelper
     */
    private $controllerHelper;

    /**
     * @var NatureOfControlTextProvider
     */
    private $natureOfControlTextProvider;

    /**
     * @var CredasValidationStatusProvider
     */
    private $credasValidationStatusProvider;

    /**
     * @var NotificationService
     */
    private $notificationService;

    /**
     * @var PositionGenerator
     */
    private $positionGenerator;

    /**
     * @var GhostService
     */
    private $ghostService;

    /**
     * @var TemplateComponentService
     */
    private $templateComponentService;

    /**
     * @var CustomerAvailability
     */
    private $customerAvailability;

    /**
     * @var Basket
     */
    private $basket;

    private WebloaderHelper $templateHelper;

    public function __construct(
        UrlGenerator $urlGenerator,
        NodeService $nodeService,
        ParameterBagInterface $config,
        CommonMarkConverter $converter,
        DiligenceLevelFactory $diligenceLevelFactory,
        ReasonProvider $reasonProvider,
        IControllerHelper $controllerHelper,
        NatureOfControlTextProvider $natureOfControlTextProvider,
        CredasValidationStatusProvider $credasValidationStatusProvider,
        NotificationService $notificationService,
        GhostService $ghostService,
        TemplateComponentService $templateComponentService,
        CustomerAvailability $customerAvailability,
        Basket $basket,
        WebloaderHelper $templateHelper
    )
    {
        $this->urlGenerator = $urlGenerator;
        $this->nodeService = $nodeService;
        $this->config = $config;
        $this->converter = $converter;
        $this->diligenceLevelFactory = $diligenceLevelFactory;
        $this->reasonProvider = $reasonProvider;
        $this->controllerHelper = $controllerHelper;
        $this->natureOfControlTextProvider = $natureOfControlTextProvider;
        $this->credasValidationStatusProvider = $credasValidationStatusProvider;
        $this->notificationService = $notificationService;
        $this->ghostService = $ghostService;
        $this->templateComponentService = $templateComponentService;
        $this->customerAvailability = $customerAvailability;
        $this->basket = $basket;
        $this->templateHelper = $templateHelper;

        $this->positionGenerator = new PositionGenerator();
    }

    public function create(Container $container, array $smartyConfig = [])
    {
        if (self::$smarty === NULL) {
            self::$smarty = new Smarty;
            self::$smarty->escape_html = TRUE;
            $templateDir = FApplication::isAdmin() ? ADMIN_TEMPLATES_DIR : FRONT_TEMPLATES_DIR;
            self::$smarty->setTemplateDir(
                [
                    $templateDir,
                    DOCUMENT_ROOT . DIRECTORY_SEPARATOR . 'project',
                    DOCUMENT_ROOT . DIRECTORY_SEPARATOR . 'project/Templates',
                    DOCUMENT_ROOT . DIRECTORY_SEPARATOR . 'vendor/made_simple/msg-framework'
                ]
            );
            self::$smarty->setCacheDir($this->config->get('smarty.cache_dir'));
            self::$smarty->setForceCompile($this->config->get('smarty.force_compile'));

            if (FApplication::isAdmin()) {
                self::$smarty->setCompileDir($this->config->get('smarty.compiled.admin.cache_dir'));
            } else {
                self::$smarty->setCompileDir($this->config->get('smarty.compiled.front.cache_dir'));
            }

            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'pr', [FTools::class, 'pr']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'h', 'htmlspecialchars');
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'cms', [FApplication::class, 'modifyCmsText']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'df', [FTools::class, 'dateFormat']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'datetime', [FTools::class, 'datetime']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'currency', [Currency::class, 'format']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'json', 'json_encode');
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'arrayFormat', [ArrayHelper::class, 'formatArr']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'jsonMessage', [ArrayHelper::class, 'formatJsonMessage']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'humanize_constants', [StringHelper::class, 'humanize_constants']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'booleanToString', [$this, 'booleanToString']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'markDown', [$this->converter, 'convertToHtml']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'checkStatusText', [$this->diligenceLevelFactory, 'getCheckStatusText']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'date', [DateHelper::class, 'formatDate']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'notAvailable', [StringHelper::class, 'notAvailable']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'idReason', [$this->reasonProvider, 'description']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'natureOfControl', [$this->natureOfControlTextProvider, 'getText']);


            self::$smarty->registerPlugin(Smarty::PLUGIN_FUNCTION, 'url', [$this, 'functionUrl']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_FUNCTION, 'returnUrlOr', [$this, 'functionReturnUrl']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_FUNCTION, 'product', [$this, 'functionGetProduct']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_FUNCTION, 'ghost', [$this, 'functionGetGhostContent']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_FUNCTION, 'message', [$this, 'functionMessage']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_FUNCTION, 'extractFormNames', [$this, 'extractFormNames']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_FUNCTION, 'getCredasStatus', [$this, 'getCredasStatus']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_FUNCTION, 'header', [$this, 'functionGetXDNAHeaderContent']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_FUNCTION, 'footer', [$this, 'functionGetXDNAFooterContent']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'str_contains', [$this, 'str_contains']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'arrayKeyExists', 'array_key_exists');
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'ucfirst', 'ucfirst');
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'array_keys', 'array_keys');
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'rawurlencode', 'rawurlencode');

            self::$smarty->registerPlugin(Smarty::PLUGIN_FUNCTION, 'styles', [$this->templateHelper, 'loadStyles']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_FUNCTION, 'scripts', [$this->templateHelper, 'loadScripts']);

            /** todo: remove */
            self::$smarty->registerPlugin(Smarty::PLUGIN_FUNCTION, 'jsonEncodeFromNames', [$this, 'jsonEncodeFromNames']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'chunkMenuItems', [$this, 'chunkMenuItems']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'sortMenuItems', [$this, 'sortMenuItems']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'totalColumnsMenuItems', [$this, 'totalColumnsMenuItems']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_FUNCTION, 'nextPosition', [$this, 'nextPosition']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_MODIFIER, 'createProductImpressionData', [$this, 'createProductImpressionData']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_BLOCK, 'productImpressionBlock', [$this, 'productImpressionBlock']);

            //self::$smarty->registerPlugin('function', 'link', function($arguments) {
            //    return FApplication::$router->link($arguments['route'], $arguments['params']);
            //});

            self::$smarty->registerPlugin(Smarty::PLUGIN_BLOCK, 'notifications', [$this, 'notifications']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_BLOCK, 'feature', [$this, 'blockFeature']);
            self::$smarty->registerPlugin(Smarty::PLUGIN_BLOCK, 'featureDisabled', [$this, 'blockFeatureDisabled']);

            if (isset($smartyConfig['plugins'])) {
                foreach ($smartyConfig['plugins'] as $name => $plugin) {
                    $callbackDefinition = new CallbackDefinition($container, $plugin['callback']);
                    self::$smarty->registerPlugin($plugin['type'] ?? 'function', $name, [$callbackDefinition, '__invoke']);
                }
            }

            $config = new WebloaderConfig(
                PROJECT_DIR . '/config/webloader/',
                WWW_DIR,
                WWW_DIR . '/webtemp/',
                WWW_DIR,
                URL_ROOT . 'webtemp'
            );
//            TemplateHelperFactory::registerMacro(
//                $config,
//                self::$smarty,
//                !$this->config->get('debug_mode')
//            );
        }

        return self::$smarty;
    }

    /**
     * @param array $params
     * @param string $content
     * @return string
     */
    public function blockFeature(array $params, $content)
    {
        if (isset($content) && isset($params['type'])) {
            if (Feature::isEnabled($params['type'])) {
                return $content;
            }
        }
    }

    /**
     * @param array $params
     * @param string $content
     * @return string
     */
    public function blockFeatureDisabled(array $params, $content)
    {
        if (isset($content) && isset($params['type'])) {
            if (Feature::isDisabled($params['type'])) {
                return $content;
            }
        }
    }

    /**
     * @param array $parameters
     * @return string
     * @throws Exception
     */
    public function functionUrl(array $parameters)
    {
        if (isset($parameters['route'])) {
            $route = $parameters['route'];
            unset($parameters['route']);
        } else {
            throw new InvalidArgumentException('No "route" parameter given for url macro');
        }

        if (isset($parameters['absolute']) && $parameters['absolute']) {
            unset($parameters['absolute']);

            return $this->urlGenerator->absoluteLink($route, $parameters);
        }

        return $this->urlGenerator->link($route, $parameters);
    }

    /**
     * @param array $parameters
     * @return string
     * @throws Exception
     */
    public function functionMessage(array $parameters)
    {
        $id = $parameters['id'] ?? null;
        if (!$id) {
            throw new InvalidArgumentException('Message id must be set for `message` macro.');
        }

        return $this->controllerHelper->getMessage($id);
    }

    /**
     * @param array $parameters
     * @return string
     * @throws Exception
     */
    public function functionReturnUrl(array $parameters)
    {
        return $this->controllerHelper->getReturnUrl() ?? $this->functionUrl($parameters);
    }

    /**
     * @param array $params
     * @param Template $template
     * @return string
     */
    public function functionGetProduct(array $params, Template $template)
    {
        if (isset($params['var'], $params['id'])) {
            $variableName = $params['var'];
            $product = $this->nodeService->getProductById($params['id']);

            $template->assign($variableName, $product);
        }
    }

    /**
     * @param array $params
     * @return string
     */
    public function functionGetGhostContent(array $params)
    {
        $slug = ArrayHelper::get($params, 'slug');
        if (isset($slug)) {
            $html = $this->ghostService->getContentBySlug($slug);
            return sprintf("<div class='ghostContentBlock %s'> %s </div>", $slug, $html);
        }
        return "<div class='ghostContentBlock'> &nbsp; </div>";
    }

    /**
     * @param array $params
     * @return string
     */
    public function functionGetXDNAHeaderContent(array $params)
    {
        if ($this->customerAvailability->optionalLoggedInCustomer()) {
            $headerContent = $this->templateComponentService->getLoggedInHeader();
        } else {
            $headerContent = $this->templateComponentService->getHeader();
        }

        $basketCount = $this->basket->getItemsCount();
        return str_replace(
            ['class="bs basket-count number">0', 'id="mobile-basket-count"></span>'],
            [sprintf('class="bs basket-count number">%s', $basketCount), sprintf('id="mobile-basket-count">%s</span>', $basketCount)],
            $headerContent
        );
    }

    /**
     * @param array $params
     * @return string
     */
    public function functionGetXDNAFooterContent(array $params)
    {
        return $this->templateComponentService->getFooter();
    }

    public function functionArrayKeyExists(string $key, array $array): bool
    {
        return array_key_exists($key, $array);
    }

    /**
     * @param boolean $value
     * @return string
     */
    public function booleanToString($value)
    {
        return $value ? 'true' : 'false';
    }

    /**
     * {extractFormNames form=$form fields=['firstName', 'forename' => 'firstName', 'lastName', 'surname' => 'lastName' , 'middleName', 'dob' => ['jsonKey' => 'dateOfBirth', 'fields' => ['year', 'month', 'day']], 'dateOfBirth' => ['year', 'month', 'day']]}
     */
    public function extractFormNames(array $params): string
    {
        $return = [];
        $return = array_merge($return, $this->extractFormNamesAsArray($params));
        $return = array_merge($return, $this->extractFormNamesAsArray($params, '1'));
        $return = array_merge($return, $this->extractFormNamesAsArray($params, '2'));

        return htmlspecialchars(json_encode($return));
    }

    /** todo: remove */
    public function jsonEncodeFromNames($fakeValue = null): string
    {
        return json_encode([
            'firstName' => "first_name",
            "lastName" => "last_name",
            "dateOfBirth" =>
                [
                    "year" => "dob_year",
                    "month" => "dob_month",
                    "day" => "dob_day"
                ]
        ]);
    }

    public function extractFormNamesAsArray(array $params, string $number = ''): array
    {
        $form = $params['form' . $number] ?? null;
        $names = $params['fields' . $number] ?? null;

        if (empty($form) || empty($names)) {
            return [];
        }

        $formFieldNames = [];
        foreach ($names as $name => $fields) {
            if (is_array($fields)) {
                if (isset($form[$name])) {
                    $jsonKey = $name;
                    $elements = $fields;
                    if (isset($fields['jsonKey'])) {
                        $jsonKey = $fields['jsonKey'];
                    }
                    if (isset($fields['fields'])) {
                        $elements = $fields['fields'];
                    }
                    $formFieldNames[$jsonKey] = $this->extractFormNamesAsArray(['form' => $form[$name], 'fields' => $elements]);
                }
            } elseif (is_string($fields)) {
                $jsonKey = $fields;
                $formName = $fields;
                if (is_string($name)) {
                    $formName = $name;
                }
                if (isset($form[$formName])) {
                    $formFieldNames[$jsonKey] = $form[$formName]->vars['full_name'];
                }
            }
        }
        return $formFieldNames;
    }

    public function getCredasStatus(array $params): string
    {
        $company = $params['company'] ?? null;
        $entityId = $params['entityId'] ?? null;

        if (!$company || !$entityId) {
            throw new InvalidArgumentException("Please provide company and entityId in params");
        }

        return htmlspecialchars(
            json_encode(
                $this->credasValidationStatusProvider
                    ->getView($company, $entityId)
            )
        );
    }

    public function chunkMenuItems(array $data): array
    {
        return array_reduce(
            $data,
            function (array $acc, array $item) {
                if (array_key_exists('col', $item)) {
                    $acc[$item['col']][] = $item;
                    return $acc;
                } else {
                    $acc[0][] = $item;
                    return $acc;
                }
            },
            []
        );
    }

    public function totalColumnsMenuItems(array $data): int
    {
        $prop = "col";
        return max(array_column($data, $prop));
    }

    public function sortMenuItems(array $data): array
    {
        usort($data,function($a,$b){
            return $a['col'] <=> $b['col'];
        });

        return $data;
    }

    public function nextPosition(array $params): int
    {
        $namespace = $params['namespace'] ?? 'default';
        return $this->positionGenerator->next($namespace);
    }

    public function createProductImpressionData(array $params, string $listName, &$repeat)
    {
        return [
            'id' => $params['packageId'] ?? $params['productId'],
            'name' => strtolower($params['title']),
            'price' => str_replace("£","",$params['price']),
            'listName' => strtolower($listName),
            'position' => $this->positionGenerator->next($listName)
        ];
    }

    public function productImpressionBlock(array $params, ?string $content, Template $template, &$repeat)
    {
        if (!$repeat && isset($content)) {
            $data = $this->createProductImpressionData($params['data'], $params['listName'],$repeat);

            $smarty = $template->getSmarty();
            $smarty->assign('impressionBlockContent',  $content);
            $smarty->assign('impressionBlockData',  $data);

            $tpl = $smarty->fetch( "Templates/utils/product_impression_block.tpl");

            //clearing the variables so they are not used outside the scope by accident
            $smarty->clearAssign('impressionBlockContent');
            $smarty->clearAssign('impressionBlockData');

            return $tpl;
        }
    }

    public function notifications(array $params, ?string $content, Template $template)
    {
        if (!isset($params['customer'])) {
            return;
        }

        $notificationData = [
            'customer' => $params['customer'],
            'company' => $params['company'] ?? null,
            'tags' => $params['tags'] ?? null
        ];

        $notifications = $this->notificationService->getViews($notificationData);
        $smarty = $template->getSmarty();
        $smarty->assign('items', $notifications);

        if(!isset($content)) {
            return;
        } elseif (empty(trim($content))) {
            return $template->getSmarty()->fetch( "NotificationModule/Templates/notifications.tpl");
        } else {
            return $content;
        }
    }

    public function str_contains($str, $needle): bool
    {
        return str_contains($str, $needle);
    }
}

<?php

namespace ProductModule\Providers;

use ProductModule\Factories\RacnViewFactory;
use ProductModule\Repositories\RacnTrackerRepository;

class RacnTrackerProvider
{
    public const BY_SERVICE = 1;
    public const BY_ORDER = 2;
    public const BY_INCLUDED_PRODUCTS = 3;
    public const RACN_INITIAL = 'RACN Initial';
    public const RACN_RENEWAL = 'RACN Renewal';

    /**
     * @var RacnTrackerRepository
     */
    private $racnTrackerRepository;

    public function __construct(RacnTrackerRepository $racnTrackerRepository)
    {
        $this->racnTrackerRepository = $racnTrackerRepository;
    }

    public function generateRacnByStatusAndCompanyId(int $status, ?int $companyId = null): array
    {
        if ($status === self::BY_ORDER) {
            return $this->generateRacnServiceView(
                $this->racnTrackerRepository->findRacnByOrder($companyId),
                self::BY_ORDER
            );
        }

        if ($status === self::BY_INCLUDED_PRODUCTS) {
            return $this->generateRacnServiceView(
                $this->racnTrackerRepository->findRacnByIncludedProducts($companyId),
                self::BY_INCLUDED_PRODUCTS
            );
        }

        return $this->generateRacnServiceView($this->racnTrackerRepository->findRacnByService($companyId));
    }

    private function generateRacnServiceView($data, ?int $status = null): array
    {
        $result = [];
        foreach ($data as $row) {
            switch ($status) {
                case self::BY_ORDER:
                    $result[] = RacnViewFactory::getRacnOrderView($row);
                    break;
                case self::BY_INCLUDED_PRODUCTS:
                    $result[] = RacnViewFactory::getRacnIncludedProductsView($row);
                    break;
                default:
                    $result[] = RacnViewFactory::getRacnServiceView($row);
            }
        }
        return $result;
    }

}
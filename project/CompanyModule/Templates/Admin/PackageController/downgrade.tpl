{extends 'AdminModule/templates/layouts/default.tpl'}

{block "tabs"}
    <ul id="navlist">
        <li><a href="{url route="551"}">List</a></li>
        <li><a href="{url route="551 view" company_id=$company->getId()}">{$company->getCompanyName()}</a></li>
        <li><a href="{url route="admin_company_package_downgrade" company=$company->getId()}" class="current">Downgrade company's package</a></li>
    </ul>
{/block}

{block content}

    <div class="flash info2">
        <b>ONLY downgrade package after receiving approval from customer.</b>
        <br><br>
        This operation will downgrade the existing package, create new services and activate them.
        <br><br>
        If you wish to refund partially for this package, you will be presented with a link after this process is done.
    </div>

    {$formHelper->start($form) nofilter}

    <fieldset id="fieldset_0">
        <legend>Start</legend>

        <table class="ff_table">
            <tbody>
            <tr>
                <th>
                    Select package
                </th>

                <td>
                    {$formHelper->widget($form['package']) nofilter}
                </td>

                <td>
                    {$formHelper->errors($form['package']) nofilter}
                </td>
            </tr>
            <tr>
                <th colspan="3">&nbsp;</th>
            </tr>
            <tr>
                <th>
                    Start of the service
                </th>

                <td>
                    {$today}
                </td>

                <td>
                </td>
            </tr>

            <tr>
                <th colspan="3">&nbsp;</th>
            </tr>
            <tr>
                <th>
                    End of the service
                </th>

                <td>
                    {$formHelper->widget($form['end']) nofilter}
                </td>

                <td>
                    {$formHelper->errors($form['end']) nofilter}
                </td>
            </tr>

            </tbody>
        </table>
    </fieldset>

    <fieldset id="fieldset_1">
        <legend>Action</legend>

        <table class="ff_table">
            <tbody><tr>
                <th></th>

                <td colspan="2"><input type="submit" name="delete" value="DOWNGRADE PACKAGE" class="btn"></td>
            </tr>
            </tbody></table>
    </fieldset>

    {$formHelper->end($form) nofilter}
{/block}

<?php

namespace CompanyModule\Queries;

use CompanyModule\Entities\Events\EmailEvent;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use DoctrineModule\SelfClearingIterator;
use Entities\Company;
use Entities\Customer;
use Entities\Service;
use Iterator;
use LoggableModule\Entities\EmailLog;
use MarketingModule\Entities\NamescoVoucher;
use OrmModule\Iterators\DoctrineIterator;
use Utils\Date;

class CompanyQueryBuilder
{
    /**
     * @var QueryBuilder
     */
    private $builder;

    public function __construct(QueryBuilder $builder)
    {
        $this->builder = $builder;
    }

    public function getCompanies(): self
    {
        $this->builder->select('c')->from(Company::class, 'c');
        return $this;
    }

    public function withIncorporationDateWithinRange(Date $intervalStart, Date $intervalEnd): self
    {
        $this->builder
            ->andWhere("c.incorporationDate > :start")
            ->andWhere("c.incorporationDate <= :end")
            ->setParameter('start', $intervalStart)
            ->setParameter('end', $intervalEnd);
        return $this;
    }

    public function withIncorporationOlderThan(Date $intervalEnd): self
    {
        $this->builder
            ->andWhere("c.incorporationDate < :intervalEnd")
            ->setParameter('intervalEnd', $intervalEnd);
        return $this;
    }

    public function withIncorporationAfter(Date $intervalStart): self
    {
        $this->builder
            ->andWhere("c.incorporationDate > :start")
            ->setParameter('start', $intervalStart);
        return $this;
    }

    public function withProductIdIn(array $productIds): self
    {
        $this->builder->andWhere("c.productId IN (:productIds)")
            ->setParameter('productIds', $productIds);
        return $this;
    }

    public function notImported(): self
    {
        $this->builder->andWhere("c.productId IS NOT NULL");
        return $this;
    }

    public function withRetailCustomer(): self
    {
        $this->builder
            ->join(Customer::class, 'cu', 'WITH', 'c.customer = cu')
            ->andWhere("cu.roleId = :retail")
            ->setParameter('retail', Customer::ROLE_NORMAL);
        return $this;
    }

    public function deleted(): self
    {
        $this->builder
            ->andWhere("c.companyNumber LIKE 'DELETED_%'")
            ->orWhere("c.deleted = 1");
        return $this;
    }

    public function withoutPSC(): self
    {
        $this->builder
            ->leftJoin(Service::class, 's', 'WITH', "(s.company = c) AND (s.serviceTypeId LIKE '%PSC%')")
            ->andWhere('s.serviceId IS NULL');
        return $this;
    }

    public function isFirstEmail(int $emailId): self
    {
        $em = $this->builder->getEntityManager();
        $expr = $em->getExpressionBuilder();

        $sub = $em->createQueryBuilder()
            ->select('ee')
            ->from(EmailEvent::class, 'ee')
            ->join(EmailLog::class,
                'el',
                Join::WITH,
                $expr->andX(
                    $expr->eq('el', 'ee.emailLog')
                )
            )
            ->where('ee.company = c')
            ->andWhere('el.emailNodeId = :emailNodeId');

        $this->builder->andWhere(
            $expr->not(
                $expr->exists($sub->getDQL())
            )
        )->setParameter('emailNodeId', $emailId);

        return $this;
    }

    public function iterate(bool $useClearingIterator = TRUE): Iterator
    {
        return $useClearingIterator ? new SelfClearingIterator($this->builder) : new DoctrineIterator($this->builder);
    }

    public function withUkCustomer(): self
    {
        $this->builder
            ->join(Customer::class, 'cu', 'WITH', 'c.customer = cu')
            ->andWhere("cu.countryId = :countryId")
            ->setParameter('countryId', Customer::UK_CITIZEN);
        return $this;
    }

    public function withoutVoucher(): self
    {
        $this
            ->builder
            ->leftJoin(NamescoVoucher::class, 'v', 'WITH', 'v.company = c')
            ->andWhere('v.id IS NULL');
        return $this;
    }

    /**
     * @return Company[]
     */
    public function getQueryResult(): array
    {
        return $this->builder->getQuery()->getResult();
    }
}

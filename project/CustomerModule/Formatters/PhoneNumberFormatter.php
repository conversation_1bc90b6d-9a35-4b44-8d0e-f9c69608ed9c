<?php

namespace CustomerModule\Formatters;

class PhoneNumberFormatter
{
    public static function convertToInternationalFormat(string $phone): string
    {
        $digitsOnly = self::sanitizePhone($phone);

        if (!self::isUkNumber($digitsOnly)) {
            return $digitsOnly;
        }

        return \sprintf('44%s', self::normalizeUkNumber($digitsOnly));
    }

    public static function convertToInternationalFormatWithPlus(string $phone): string
    {
        $digitsOnly = self::sanitizePhone($phone);

        if (!self::isUkNumber($digitsOnly)) {
            return $digitsOnly;
        }

        return \sprintf('+44%s', self::normalizeUkNumber($digitsOnly));
    }

    public static function convertToLocalFormat(string $phone): string
    {
        $digitsOnly = self::sanitizePhone($phone);

        if (strlen($digitsOnly) < 9) {
            return $digitsOnly;
        }

        $digitsOnly = preg_replace('/^(?:00|\+)?44/', '', $digitsOnly);

        return \sprintf('0%s', ltrim($digitsOnly, '0'));
    }

    private static function sanitizePhone(string $phone): string
    {
        return preg_replace("/\D/", "", trim($phone));
    }

    private static function isUkNumber(string $digitsOnly): bool
    {
        return preg_match('/^(00|\+)?44/', $digitsOnly) || str_starts_with($digitsOnly, '0');
    }

    private static function normalizeUkNumber(string $digitsOnly): string
    {
        $digitsOnly = preg_replace('/^(00|\+)?44(0)?/', '', $digitsOnly);

        return ltrim($digitsOnly, '0');
    }
}
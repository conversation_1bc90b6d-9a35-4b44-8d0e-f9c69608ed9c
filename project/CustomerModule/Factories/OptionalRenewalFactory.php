<?php

namespace CustomerModule\Factories;

use Symfony\Component\HttpFoundation\ParameterBag;
use CustomerModule\Entities\Settings\OptionalRenewalSetting;

class OptionalRenewalFactory
{
    public function getOptionalRenewalData(ParameterBag $queryParams): array
    {
        $options = [];

        foreach ($queryParams->getIterator() as $key => $param) {
            if ($key === OptionalRenewalSetting::AVAILABLE_TO_ALL_PRODUCTS) {
                $options[$key] = $param;
            } else if ($key === OptionalRenewalSetting::AVAILABLE_PRODUCTS) {
                $options[$key] = $param;
            }
        }

        return $options;
    }
}

<?php

namespace BasketModule\Views;

use Entities\Service;
use Libs\Basket;
use BasketModule\Deciders\ActionRouteDecider;
use BasketModule\Deciders\GuaranteedSameDayDecider;
use BasketModule\Deciders\OfferDecider;
use Models\Products\BasketProduct;
use FeatureModule\Feature;
use Generator;
use Models\Products\Package;
use Services\PackageService;
use Services\ProductService;
use Symfony\Component\HttpFoundation\Request;
use UserModule\Contracts\ICustomer;

class PackageBasketView
{
    /**
     * @var Basket
     */
    private $basket;

    /**
     * @var Request
     */
    private $request;

    /**
     * @var GuaranteedSameDayDecider
     */
    private $guaranteedSameDayDecider;

    /**
     * @var OfferDecider
     */
    private $offerDecider;

    /**
     * @var PackageService
     */
    private $packageService;

    /**
     * @var ProductService
     */
    private $productService;

    /**
     * @var ActionRouteDecider
     */
    private $routeDecider;

    /**
     * @param Basket $basket
     * @param GuaranteedSameDayDecider $guaranteedSameDayDecider
     * @param Request $request
     * @param OfferDecider $offerDecider
     * @param PackageService $packageService
     * @param ProductService $productService
     * @param ActionRouteDecider $routeDecider
     */
    public function __construct(
        Basket $basket,
        GuaranteedSameDayDecider $guaranteedSameDayDecider,
        Request $request,
        OfferDecider $offerDecider,
        PackageService $packageService,
        ProductService $productService,
        ActionRouteDecider $routeDecider
    )
    {
        $this->basket = $basket;
        $this->request = $request;
        $this->guaranteedSameDayDecider = $guaranteedSameDayDecider;
        $this->offerDecider = $offerDecider;
        $this->packageService = $packageService;
        $this->productService = $productService;
        $this->routeDecider = $routeDecider;
    }

    /**
     * @return bool
     */
    public function canShowVoucherForm()
    {
        return !$this->basket->getVoucher()
            && ($this->request->cookies->get('upgrade-voucher-form') || $this->request->query->has('redeem'));
    }

    /**
     * @return bool
     */
    public function canAddGuaranteedSameDay()
    {
        return $this->guaranteedSameDayDecider->canBeAdded();
    }

    /**
     * @return bool
     */
    public function canAddOffer()
    {
        return $this->offerDecider->canAddOffer();
    }

    /**
     * @return BasketProduct
     */
    public function getOffer()
    {
        return $this->offerDecider->getOffer();
    }

    /**
     * @return string
     */
    public function getOfferIdFromMasterList()
    {
        return $this->offerDecider->getOfferIdFromMasterList();
    }

    /**
     * @return Generator
     */
    public function getUpsellProducts()
    {
        $firstBasketItem = $this->basket->getPackageOrNull() ?? $this->basket->getFirstItem();

        if ($firstBasketItem) {
            $associatedProducts = $this->productService->getAssociatedProductsNotInBasket($firstBasketItem, $this->basket);
            if ($associatedProducts) {
                foreach ($associatedProducts as $product) {
                    if (!$this->basket->hasProduct($product->getId())) {
                        yield $product->getId() => $product;
                    }
                }
            }
        }
    }

    /**
     * @return bool
     */
    public function hasToShowProgressBar()
    {
        return !$this->basket->isEmpty() && $this->basket->getFirstItem() instanceof Package;
    }

    /**
     * @param ICustomer|NULL $customer
     * @return string
     */
    public function getActionUrl(ICustomer $customer = NULL)
    {
        return $this->routeDecider->getUrl($this->basket, $customer);
    }

    public function isOmnipay(): string {
        return Feature::isEnabled('omnipay');
    }

    public function getBasicPackagePrice(): float {
        return $this->productService->getBasicPackagePrice();
    }
}

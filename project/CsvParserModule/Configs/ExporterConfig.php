<?php

namespace CsvParserModule\Configs;

use CsvParserModule\CsvFileObject;

class ExporterConfig
{
    private string $delimiter = ',';
    private string $enclosure = '"';
    private string $escape = '\\';
    private string $newline = "\r\n";
    private string $fromCharset = 'auto';
    private mixed $toCharset = null;
    private string $fileMode = CsvFileObject::FILE_MODE_WRITE;
    private array $columnHeaders = [];

    public function setDelimiter($delimiter): static
    {
        $this->delimiter = $delimiter;
        return $this;
    }

    public function getDelimiter(): string
    {
        return $this->delimiter;
    }

    public function setEnclosure($enclosure): static
    {
        $this->enclosure = $enclosure;
        return $this;
    }

    public function getEnclosure(): string
    {
        return $this->enclosure;
    }

    public function setEscape($escape): static
    {
        $this->escape = $escape;
        return $this;
    }

    public function getEscape(): string
    {
        return $this->escape;
    }

    public function setNewline($newline): static
    {
        $this->newline = $newline;
        return $this;
    }

    public function getNewline(): string
    {
        return $this->newline;
    }

    public function setFromCharset($fromCharset): static
    {
        $this->fromCharset = $fromCharset;
        return $this;
    }

    public function getFromCharset(): string
    {
        return $this->fromCharset;
    }

    public function setToCharset($toCharset): static
    {
        $this->toCharset = $toCharset;
        return $this;
    }

    public function getToCharset(): null
    {
        return $this->toCharset;
    }

    public function setFileMode(string $fileMode): static
    {
        $this->fileMode = $fileMode;
        return $this;
    }

    public function getFileMode(): string
    {
        return $this->fileMode;
    }

    public function setColumnHeaders(array $columnHeaders): static
    {
        $this->columnHeaders = $columnHeaders;
        return $this;
    }

    public function getColumnHeaders(): array
    {
        return $this->columnHeaders;
    }
}
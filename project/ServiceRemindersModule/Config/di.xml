<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />
        <service class="ServiceRemindersModule\Commands\SendAutoRenewalRemindersCommand" id="service_reminders_module.commands.send_auto_renewal_reminders_command">
            <argument id="router_module.helpers.controller_helper" type="service"/>
            <argument id="service_reminders_module.providers.auto_renewal_reminders_provider" type="service"/>
            <argument id="service_reminders_module.processors.services_reminder_processor" type="service"/>
            <argument id="service_settings_module.services.service_settings_service" type="service"/>

            <tag name="cron.command" command-name="services:reminders:send_auto" action="execute" />
        </service>

        <service class="ServiceRemindersModule\Deciders\AddressEligibilityDecider" id="service_reminders_module.deciders.address_eligibility_decider"/>
        <service class="ServiceRemindersModule\Deciders\AddressReminderDecider" id="service_reminders_module.deciders.address_reminder_decider">
            <argument id="service_reminders_module.deciders.address_eligibility_decider" type="service"/>
        </service>
        <service class="ServiceRemindersModule\Deciders\AbandonedBasketDecider" id="service_reminders_module.deciders.abandoned_basket_decider">
            <argument id="email_module.repositories.predefined_email_repository" type="service"/>
            <argument id="mailgun_module.repositories.mailgun_repository" type="service"/>
        </service>


        <service class="ServiceRemindersModule\Factories\ReminderServicesFactory" id="service_reminders_module.factories.reminder_services_factory"/>

        <service class="ServiceRemindersModule\Filters\AddressServicesFilter" id="service_reminders_module.filters.address_services_filter">
            <argument id="service_reminders_module.deciders.address_eligibility_decider" type="service"/>
        </service>

        <service class="ServiceRemindersModule\Filters\EventServicesFilter" id="service_reminders_module.filters.event_services_filter">
            <argument id="services.event_service" type="service"/>
        </service>

        <service class="ServiceRemindersModule\Processors\ServicesReminderProcessor" id="service_reminders_module.processors.services_reminder_processor">
            <argument id="service_reminders_module.providers.email_provider" type="service"/>
            <argument id="services.event_service" type="service"/>
            <argument id="email_module.gateways.default_gateway" type="service"/>
        </service>

        <service class="ServiceRemindersModule\Providers\AutoRenewalRemindersProvider" id="service_reminders_module.providers.auto_renewal_reminders_provider">
            <argument>%service_reminders_module.auto_renewal_reminders%</argument>
            <argument id="services.service_service" type="service"/>
            <argument id="service_reminders_module.factories.reminder_services_factory" type="service"/>
        </service>

        <service class="ServiceRemindersModule\Providers\BenefitsProvider" id="service_reminders_module.providers.benefits_provider">
            <argument id="service_reminders_module.repositories.benefits_repository" type="service"/>
        </service>

        <service class="ServiceRemindersModule\Providers\EmailProvider" id="service_reminders_module.providers.email_provider">
            <argument id="femail_factory" type="service"/>
            <argument id="service_reminders_module.providers.benefits_provider" type="service"/>
            <argument id="template_module.engines.latte_template_engine" type="service"/>
            <argument>%service_reminders_module.reminders_template_path%</argument>
            <argument id="user_module.creators.one_time_password_auth_token_creator" type="service"/>
            <argument id="repositories.service_repository" type="service"/>
            <argument id="repositories.transaction_repository" type="service"/>
            <argument id="services.node_service" type="service"/>
            <argument id="url_generator" type="service"/>
            <argument id="user_module.creators.one_time_password_auth_token_creator" type="service"/>
        </service>

        <service class="ServiceRemindersModule\Deciders\ServiceAddressTypeDecider" id="service_reminders_module.deciders.service_address_type_decider">
            <argument id="companies_house_module.repositories.member_repository" type="service"/>
        </service>

        <service class="ServiceRemindersModule\Providers\RemindersProvider" id="service_reminders_module.providers.reminders_provider">
            <argument>%service_reminders_module.reminders%</argument>
            <argument id="service_reminders_module.filters.address_services_filter" type="service"/>
            <argument id="service_reminders_module.deciders.address_reminder_decider" type="service"/>
            <argument id="service_reminders_module.factories.reminder_services_factory" type="service"/>
            <argument id="service_reminders_module.filters.event_services_filter" type="service"/>
            <argument id="service_reminders_module.checkers.credit_control_requirement_checker" type="service"/>
            <argument id="service_reminders_module.deciders.service_address_type_decider" type="service"/>
            <argument id="service_settings_module.services.service_settings_service" type="service"/>
            <argument id="services.service_service" type="service"/>
        </service>

        <service class="ServiceRemindersModule\Repositories\BenefitsRepository" id="service_reminders_module.repositories.benefits_repository">
            <argument>%service_reminders_module.benefits%</argument>
        </service>

        <service class="ServiceRemindersModule\Services\AutoRenewalEmailService" id="service_reminders_module.services.auto_renewal_email_service">
            <argument id="service_reminders_module.factories.reminder_services_factory" type="service"/>
            <argument id="email_module.gateways.default_gateway" type="service"/>
            <argument id="service_reminders_module.providers.email_provider" type="service"/>
        </service>

        <service class="ServiceRemindersModule\Checkers\CreditControlRequirementChecker" id="service_reminders_module.checkers.credit_control_requirement_checker">
            <argument id="companies_house_module.repositories.member_repository" type="service"/>
        </service>

        <service class="ServiceRemindersModule\Emailers\AbandonedBasketEmailer" id="service_reminders_module.emailers.abandoned_basket_emailer">
            <argument id="email_module.repositories.predefined_email_repository" type="service"/>
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
            <argument id="mailgun_module.repositories.mailgun_repository" type="service"/>

        </service>

        <service class="ServiceRemindersModule\Processors\AbandonedBasketProcessor" id="service_reminders_module.processors.abandoned_basket_processor">
            <argument id="service_reminders_module.emailers.abandoned_basket_emailer" type="service"/>
            <argument id="email_module.gateways.marketing_gateway" type="service"/>
            <argument id="cron.loggers.default_logger" type="service"/>
            <argument id="service_reminders_module.deciders.abandoned_basket_decider" type="service"/>
        </service>

        <service class="ServiceRemindersModule\Facades\AbandonedBasketFacade" id="service_reminders_module.facades.abandoned_basket_facade">
            <argument id="payment_flow_module.repositories.payment_flow_repository" type="service"/>
            <argument id="repositories.customer_repository" type="service"/>
            <argument id="url_generator" type="service"/>
            <argument id="services.node_service" type="service"/>
            <argument id="cron.loggers.default_logger" type="service"/>
            <argument id="service_reminders_module.processors.abandoned_basket_processor" type="service"/>
        </service>

        <service class="ServiceRemindersModule\Commands\AbandonedBasketCommand" id="service_reminders_module.commands.abandoned_basket_command">
            <argument id="cron.loggers.default_logger" type="service"/>
            <argument id="payment_flow_module.repositories.payment_flow_repository" type="service"/>
            <argument id="service_reminders_module.facades.abandoned_basket_facade" type="service"/>

            <tag name="cron.command" command-name="services:reminders:abandoned_basket" action="sendEmailReminderOfBasket" />
        </service>



    </services>
</container>

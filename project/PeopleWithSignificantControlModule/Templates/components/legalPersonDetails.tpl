<div class="fieldset brick-desktop btm20">
    <h3 class="top0">Legal person</h3>
    
    <div class="form-group {if $legalPerson['legalPersonName']->vars['errors']->count() > 0} has-error{/if}">
        {$formHelper->label($legalPerson['legalPersonName']) nofilter}
        <span class="help-button">
            <a data-toggle="tooltip" data-placement="bottom" title=""
               data-original-title="
            {if $companyType != 'LLP'}
                This is the company name of the company you want to stand in the
                    position of director. Please note that you cannot nominate the company
                    you are currently forming to be a director of itself.
            {else}
                This is the name of the company you want to appoint as a member. Please
                    note that you cannot nominate the company you are currently forming to
                    be a member of itself.
            {/if}
            "><i class="fa fa-info-circle"></i></a>
        </span>
        {$formHelper->widget($legalPerson['legalPersonName']) nofilter}
        {$formHelper->errors($legalPerson['legalPersonName']) nofilter}
    </div>

    <div class="form-group {if $legalPerson['lawGoverned']->vars['errors']->count() > 0} has-error{/if}">
        {$formHelper->label($legalPerson['lawGoverned']) nofilter}
        <span class="help-button">
            <a data-toggle="tooltip" data-placement="bottom" title=""
               data-original-title="Enter which law governs the company you are appointing. Eg: Common, Civil
            etc."><i class="fa fa-info-circle"></i></a>
        </span>
        {$formHelper->widget($legalPerson['lawGoverned']) nofilter}
        {$formHelper->errors($legalPerson['lawGoverned']) nofilter}
    </div>

    <div class="form-group {if $legalPerson['legalForm']->vars['errors']->count() > 0} has-error{/if}">
        {$formHelper->label($legalPerson['legalForm']) nofilter}
        <span class="help-button">
            <a data-toggle="tooltip" data-placement="bottom" title=""
               data-original-title="Legal form of the legal person (E.g. Government department)"><i class="fa fa-info-circle"></i></a>
        </span>
        {$formHelper->widget($legalPerson['legalForm']) nofilter}
        {$formHelper->errors($legalPerson['legalForm']) nofilter}
    </div>
</div>
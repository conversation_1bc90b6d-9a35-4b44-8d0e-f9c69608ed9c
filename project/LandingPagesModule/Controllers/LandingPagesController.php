<?php

namespace LandingPagesModule\Controllers;

use FeedbackModule\Providers\FeedbackProvider;
use FrontModule\controlers\SearchControler;
use Symfony\Component\HttpFoundation\Response;
use TemplateModule\Renderers\IRenderer;

class LandingPagesController
{
    /**
     * @var IRenderer
     */
    private $renderer;
    
    /**
     * @var FeedbackProvider
     */
    private $feedbackProvider;

    public function __construct(IRenderer $renderer, FeedbackProvider $feedbackProvider)
    {
        $this->renderer = $renderer;
        $this->feedbackProvider = $feedbackProvider;
    }
    
    public function landingPrivacy(): Response
    {
        return $this->renderLandingPage();
    }
    
    public function landingContractors(): Response
    {
        return $this->renderLandingPage();    
    }
    
    public function landingRegistrationPicking(): Response
    {
        return $this->renderLandingPage();
    }
    
    public function landingRegisteredOffice(): Response
    {
        return $this->renderLandingPage();
    }

    public function landingAppointmentRequired(): Response
    {
        return $this->renderLandingPage();
    }
    
    public function landingGiftACompany(): Response
    {
        return $this->renderLandingPage();
    }

    private function renderLandingPage(): Response
    {
        $feefo = $this->feedbackProvider->getFeefo();
        return $this->renderer->render([
                'namesearch' => (bool) SearchControler::getCompanyName(),
                'feefoAverage' => $feefo->getRating(),
                'feefoReviewCount' => $feefo->getTotalReviews(),
        ]);
    }
}

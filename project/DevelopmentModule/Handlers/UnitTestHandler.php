<?php

namespace DevelopmentModule\Handlers;

use CompaniesHouseModule\Deciders\CHExtraFeeChargeDecider;
use CompaniesHouseModule\Deciders\CompaniesHouseReadinessDecider;
use DateTime;
use Entities\Company;

class UnitTestHandler
{
    public function __construct(
        readonly private CompaniesHouseReadinessDecider $companiesHouseReadinessDecider,
        readonly private CHExtraFeeChargeDecider $CHExtraFeeChargeDecider
    ) {}

    public function isValidEnglishString(string $value): bool
    {
        return preg_match('/^[\x20-\x7F£]*$/u', $value);
    }

    public function isChargable(Company $company): bool
    {
        if ($this->companiesHouseReadinessDecider->isAble($company))
            return true;

        if ($this->CHExtraFeeChargeDecider->isIncorporationExtraFeeChargeApplicable($company))
            return true;

        return false;
    }

    public function isChargableWithExtraConditions(Company $company): bool
    {
        if (!$this->doesCompanyMeetExtraConditions($company)) return false;

        if ($this->companiesHouseReadinessDecider->isAble($company))
            return true;

        if ($this->CHExtraFeeChargeDecider->isIncorporationExtraFeeChargeApplicable($company))
            return true;

        return false;
    }

    private function doesCompanyMeetExtraConditions(Company $company): bool
    {
        if ($company->getDtc() > new DateTime('2024-01-01 00:00:00'))
            return $company->isIncorporated();

        return $company->isIncorporated() && $company->isActive();
    }


}
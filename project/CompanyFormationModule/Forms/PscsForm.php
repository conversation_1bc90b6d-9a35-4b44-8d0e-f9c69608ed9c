<?php

namespace CompanyFormationModule\Forms;

use CompanyFormationModule\Dto\PscsData;
use Models\Products\Product;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class PscsForm extends AbstractType
{
    const TYPE_MSG_PSC_REGISTER = Product::PRODUCT_PSC_REGISTER_UPSELL;
    const TYPE_SELF_PSC_REGISTER = -1;

    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        if ($options['isUpsellEligible']) {
            $builder
                ->add(
                    'type',
                    ChoiceType::class,
                    [
                        'required' => FALSE,
                        'choices' => array_flip([
                            self::TYPE_MSG_PSC_REGISTER => 'I’d like to store my PSC register with Company Formation Made Simple ',
                            self::TYPE_SELF_PSC_REGISTER => 'I will complete and store my own PSC Register'
                        ]),
                        'expanded' => TRUE,
                        'multiple' => FALSE,
                    ]
                );
        }

    }

    /**
     * @return string
     */
    public function getName()
    {
        return 'PscsForm';
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(
            [
                'data_class' => PscsData::class,
                'isUpsellEligible' => FALSE,
                'validation_groups' => function (FormInterface $form) {

                    $groups = ['Default'];

                    if ($form->getConfig()->getOption('isUpsellEligible')) {
                        $groups[] = 'Upsell';
                    }
                    return $groups;
                },
            ]
        );
    }
}

<?php

declare(strict_types=1);

namespace CompanyFormationModule\Entities;

use CompaniesHouseModule\Entities\IAddress;
use CompaniesHouseModule\Entities\IPersonalDetails;
use CompaniesHouseModule\Entities\PersonalDetails;
use CompaniesHouseModule\Entities\IPersonMember;
use Doctrine\ORM\Mapping as Orm;
use Entities\CompanyHouse\Helper\Authentication;
use FormModule\Helpers\Country;

/**
 * @Orm\Entity
 */
class DirectorPerson extends Member implements IDirector, IPersonMember
{
    protected $type = 'DIR';

    protected $corporate = 0;
    /**
     * @var IPersonalDetails
     *
     * @Orm\Embedded(columnPrefix=false, class="CompaniesHouseModule\Entities\PersonalDetails")
     */
    private $personalDetails;

    /**
     * @var Authentication
     *
     * @Orm\Column(type="Entities\CompanyHouse\Helper\Authentication")
     */
    private $authentication;

    /**
     * @var IAddress
     *
     * @Orm\Embedded(columnPrefix="residential_", class="CompaniesHouseModule\Entities\Address")
     */
    private $residentialAddress;

    public function __construct(
        IPersonalDetails $personalDetails,
        IAddress $address,
        IAddress $residentialAddress,
        ?Authentication $authentication = null,
    ) {
        $this->personalDetails = $personalDetails;
        $this->residentialAddress = $residentialAddress;
        $this->address = $address;
        $this->authentication = $authentication;
    }

    public function getPersonalDetails(): IPersonalDetails
    {
        return $this->personalDetails;
    }

    public function setPersonalDetails(IPersonalDetails $personalDetails): void
    {
        $this->personalDetails = $personalDetails;
    }

    public function getResidentialAddress(): IAddress
    {
        return $this->residentialAddress;
    }

    public function setResidentialAddress(IAddress $residentialAddress): void
    {
        $this->residentialAddress = $residentialAddress;
    }

    public function getAuthentication(): ?Authentication
    {
        return $this->authentication;
    }

    public function setAuthentication(?Authentication $authentication): void
    {
        $this->authentication = $authentication;
    }

    public function getName(): string
    {
        return $this->personalDetails->getFullName();
    }

    public function hasCountryOfResidence(): bool
    {
        return (bool) $this->personalDetails->getCountryOfResidence();
    }

    public function hasPremise(): bool
    {
        return (bool) $this->residentialAddress->getPremise();
    }

    public function hasResidentialAddressWithMainFields(): bool
    {
        return $this->residentialAddress->hasMainFields();
    }

    public function getCountryOfResidence(): ?string
    {
        return $this->personalDetails->getCountryOfResidence();
    }

    public function getPostcode(): string
    {
        return $this->residentialAddress->getPostcode();
    }

    public function isAuthenticationEmpty(): bool
    {
        return empty($this->authentication?->getBirthTown()); /* @phpstan-ignore-line */
    }

    public function isUkResident(): bool
    {
        return $this->personalDetails->getCountryOfResidenceIso() === Country::UNITED_KINGDOM;
    }

    public function isAdult(): bool
    {
        return $this->personalDetails->getDob()->isBetween('-100 years', '-18 years');
    }

    public function isBelow18YearsOld(): bool
    {
        if ($this->personalDetails instanceof PersonalDetails) {
            return $this->personalDetails->isBelow18YearsOld();
        }

        return false;
    }
}

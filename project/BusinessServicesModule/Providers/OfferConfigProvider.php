<?php

namespace BusinessServicesModule\Providers;

use BusinessServicesModule\Entities\Offer;
use BusinessServicesModule\Exceptions\EntityConfigNotFoundException;
use BusinessServicesModule\Repositories\IOfferRepository;

class OfferConfigProvider
{
    /**
     * @var IOfferRepository
     */
    private $repository;

    public function __construct(IOfferRepository $repository)
    {
        $this->repository = $repository;
    }

    public function getOffer(int $id): Offer
    {
        $offer = $this->repository->findOneById($id);

        if(!$offer){
            throw EntityConfigNotFoundException::offer($id);
        }

        return $offer;
    }
}
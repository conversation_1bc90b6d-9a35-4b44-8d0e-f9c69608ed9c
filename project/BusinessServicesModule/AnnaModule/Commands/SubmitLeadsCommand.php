<?php

namespace BusinessServicesModule\AnnaModule\Commands;

use BusinessServicesModule\AnnaModule\Factories\LeadDataCreateFactory;
use BusinessServicesModule\AnnaModule\Factories\LeadsToProcessCriteriaFactory;
use BusinessServicesModule\ApiClient\BusinessServicesApiClient;
use BusinessServicesModule\Entities\Lead;
use BusinessServicesModule\Exceptions\InvalidArgumentException;
use BusinessServicesModule\Repositories\ArrayOfferRepository;
use BusinessServicesModule\Repositories\LeadRepository;
use Exception;
use HttpClient\Exceptions\RequestException;
use Psr\Log\LoggerInterface;

/**
 * @description Sends leads for ANNA offers
 */
class SubmitLeadsCommand
{
    /**
     * @var LeadRepository
     */
    private $leadRepository;

    /**
     * @var LeadsToProcessCriteriaFactory
     */
    private $criteriaFactory;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var LeadDataCreateFactory
     */
    private $dataCreateFactory;

    /**
     * @var BusinessServicesApiClient
     */
    private $apiClient;

    public function __construct(
        LeadRepository $leadRepository,
        LeadsToProcessCriteriaFactory $criteriaFactory,
        LoggerInterface $logger,
        LeadDataCreateFactory $dataCreateFactory,
        BusinessServicesApiClient $apiClient
    ) {
        $this->leadRepository = $leadRepository;
        $this->criteriaFactory = $criteriaFactory;
        $this->logger = $logger;
        $this->dataCreateFactory = $dataCreateFactory;
        $this->apiClient = $apiClient;
    }

    public function execute(bool $dryRun = true): void
    {
        if ($dryRun) {
            $this->logger->debug('Running in DRY-RUN mode.');
        }

        $leads = $this->leadRepository->findLeadsToProcess($this->criteriaFactory->createDefaultLeads());
        $countSuccess = 0;
        $countFails = 0;

        $this->logger->info(sprintf('There is %d Anna leads to be processed', count($leads)));

        foreach ($leads as $lead) {
            try {
                $data = $this->dataCreateFactory->createDefaultLeadData($lead);

                if (!$dryRun) {
                    $this->sendLead($lead, $data) ? $countSuccess++ : $countFails++;
                } else {
                    $this->logger->debug(
                        sprintf('Anna lead (ID: %d) to be processed', $lead->getId()),
                        [
                            'id' => $lead->getId(),
                            'company' => $lead->getCompany()->getId(),
                            'customer' => $lead->getCustomer()->getId(),
                            'data' => $data
                        ]
                    );
                }
            } catch (Exception $e) {
                $this->logError($e->getMessage(),
                    [
                        'id' => $lead->getId(),
                        'company' => $lead->getCompany()->getId(),
                        'customer' => $lead->getCustomer()->getId(),
                        'exception' => $e
                    ]
                );
            }
        }

        $this->logger->debug(
            sprintf(
                'Command ended. %d Anna leads were processed. %d were sent successfully and %d failed.',
                count($leads),
                $countSuccess,
                $countFails
            )
        );
    }

    private function sendLead(Lead $lead, array $data): bool
    {
        try {

            $response = $this->apiClient->sendRequest($lead, ArrayOfferRepository::ANNA_MEDIATOR_ID, $data);

            if ($response->isSuccess()) {
                $lead->markAsProcessed();
                $this->leadRepository->saveEntity($lead);
                $this->logger->debug(
                    sprintf('Anna lead (ID: %d) processed successfully', $lead->getId()),
                    [
                        'id' => $lead->getId(),
                        'company' => $lead->getCompany()->getId(),
                        'customer' => $lead->getCustomer()->getId(),
                    ]
                );
                return true;
            } else {
                $this->logError(
                    $response->getBody(),
                    [
                        'id' => $lead->getId(),
                        'company' => $lead->getCompany()->getId(),
                        'customer' => $lead->getCustomer()->getId(),
                        'response' => $response
                    ]
                );
            }
        } catch (InvalidArgumentException | RequestException $e) {
            $this->logError($e->getMessage(),
                [
                    'id' => $lead->getId(),
                    'company' => $lead->getCompany()->getId(),
                    'customer' => $lead->getCustomer()->getId(),
                    'exception' => $e
                ]
            );
        }

        return false;
    }

    private function logError(string $message = null, array $data = []): void
    {
        $this->logger->error(
            sprintf(
                'Lead was not processed - %s',
                $message ?? 'Unknown Error'
            ),
            $data
        );
    }

}

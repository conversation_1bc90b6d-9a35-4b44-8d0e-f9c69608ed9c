<?php

namespace SmokeTestsModule\Commands;

use Psr\Log\LoggerInterface;
use SmokeTestsModule\Factories\TableNodeFactory;
use SmokeTestsModule\Providers\ApiRouterUrlsProvider;
use SmokeTestsModule\Providers\ConfigProvider;
use SmokeTestsModule\Providers\DynamicUrlsProvider;
use SmokeTestsModule\Providers\PagesUrlsProvider;
use SmokeTestsModule\Providers\RouterUrlsProvider;
use Symfony\Component\Filesystem\Filesystem;

class GenerateTestsCommand
{
    public const DEFAULT_AMOUNT_OF_URLS = 25;
    public const SOURCE_ROUTE = 1;
    public const SOURCE_PAGE = 2;
    public const SOURCE_DYNAMIC = 3;
    public const SOURCE_ROUTE_API = 4;

    /**
     * @var ConfigProvider
     */
    private $configProvider;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var PagesUrlsProvider
     */
    private $pagesUrlsProvider;

    /**
     * @var RouterUrlsProvider
     */
    private $routerUrlsProvider;

    /**
     * @var DynamicUrlsProvider
     */
    private $dynamicUrlsProvider;

    /**
     * @var ApiRouterUrlsProvider
     */
    private $apiRouterUrlsProvider;

    public function __construct(
        ConfigProvider        $configProvider,
        LoggerInterface       $logger,
        PagesUrlsProvider     $pagesUrlsProvider,
        RouterUrlsProvider    $routerUrlsProvider,
        DynamicUrlsProvider   $dynamicUrlsProvider,
        ApiRouterUrlsProvider $apiRouterUrlsProvider
    )
    {
        $this->configProvider = $configProvider;
        $this->logger = $logger;
        $this->pagesUrlsProvider = $pagesUrlsProvider;
        $this->routerUrlsProvider = $routerUrlsProvider;
        $this->dynamicUrlsProvider = $dynamicUrlsProvider;
        $this->apiRouterUrlsProvider = $apiRouterUrlsProvider;
    }

    public function execute(int $amountOfUrls = self::DEFAULT_AMOUNT_OF_URLS, int $fromRoutes = self::SOURCE_ROUTE): void
    {
        $source = $this->configProvider->getFeatureFilesPath($fromRoutes);
        $this->logger->info(sprintf('Generating feature files from `%s`', $source));

        $urls = $this->getUrls($fromRoutes);

        $this->logger->info(sprintf('Extracted `%d` URLs from `%s`', count($urls), $source));

        $urlsChunks = array_chunk($urls, $amountOfUrls);
        foreach ($urlsChunks as $index => $chunk) {
            $tableNode = TableNodeFactory::generateTableFromArray($chunk);

            $filePath = $this->configProvider->getFeatureFilesPath($fromRoutes);

            switch ($fromRoutes) {
                case self::SOURCE_PAGE:
                    $fileName = sprintf('from_page_repo_%d.feature', $index);
                    break;
                case self::SOURCE_DYNAMIC:
                    $fileName = sprintf('from_dynamic_pages_%d.feature', $index);
                    break;
                case self::SOURCE_ROUTE_API:
                    $fileName = sprintf('from_router_api_%d.feature', $index);
                    break;
                default:
                    $fileName = sprintf('from_router_%d.feature', $index);
                    break;
            }

            $fileSystem = new Filesystem();
            if (!$fileSystem->exists($filePath)) {
                $fileSystem->mkdir($filePath, 0777);
            }

            $tableString = str_replace("\n", "\n\t\t\t", $tableNode->getTableAsString());
            $scenarios = sprintf("%s\t\t\t%s", $this->configProvider->getScenarioOutline(), $tableString);
            file_put_contents(sprintf('%s%s', $filePath, $fileName), $scenarios);
        }

        $this->logger->info('Done!');
    }

    public function getUrls(int $source): array
    {
        switch ($source) {
            case self::SOURCE_ROUTE:
                return $this->routerUrlsProvider->getUrlsAsArray();
            case self::SOURCE_PAGE:
                return $this->pagesUrlsProvider->getUrlsAsArray();
            case self::SOURCE_DYNAMIC:
                return $this->dynamicUrlsProvider->getUrlsAsArray();
            case self::SOURCE_ROUTE_API:
                return $this->apiRouterUrlsProvider->getUrlsAsArray();
            default:
                return [];
        }
    }
}
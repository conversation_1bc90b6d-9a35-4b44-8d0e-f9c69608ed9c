<?php

namespace spec\CustomerModule\Repositories;

use CustomerModule\Repositories\InvoiceAddressRepository;
use Doctrine\ORM\Mapping\ClassMetadata;
use Doctrine\ORM\EntityManager;
use PhpSpec\ObjectBehavior;

/**
 * @mixin InvoiceAddressRepository
 */
class InvoiceAddressRepositorySpec extends ObjectBehavior
{
    function let(EntityManager $entityManager, ClassMetadata $metadata)
    {
        $this->beConstructedWith($entityManager, $metadata);
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(InvoiceAddressRepository::class);
    }
}

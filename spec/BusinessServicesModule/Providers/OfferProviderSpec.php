<?php

namespace spec\BusinessServicesModule\Providers;

use BusinessServicesModule\Entities\Offer;
use BusinessServicesModule\Providers\IOfferProvider;
use BusinessServicesModule\Providers\OfferProvider;
use BusinessServicesModule\Repositories\ArrayOfferRepository;
use BusinessServicesModule\Repositories\LeadRepository;
use Entities\Company;
use PhpSpec\ObjectBehavior;
use Psr\Log\LoggerInterface;

class OfferProviderSpec extends ObjectBehavior
{
    /**
     * @var IOfferProvider[]
     */
    private $providers = [];

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var LeadRepository
     */
    private $leadRepository;

    /**
     * @var ArrayOfferRepository
     */
    private $offerRepository;

    function let(LoggerInterface $logger, IOfferProvider $provider1, IOfferProvider $provider2, LeadRepository $leadRepository, ArrayOfferRepository $offerRepository)
    {
        $this->logger = $logger;
        $this->leadRepository = $leadRepository;
        $this->offerRepository = $offerRepository;
        $this->beConstructedWith($logger, $leadRepository, $offerRepository);

        $this->providers = [$provider1, $provider2];
        $this->addProvider($provider1);
        $this->addProvider($provider2);
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(OfferProvider::class);
    }

    function it_should_return_offers(Company $company, Offer $offer1, Offer $offer2, Offer $offer3)
    {
        $offer1->getOrder()->willReturn(2);
        $offer2->getOrder()->willReturn(1);
        $offer3->getOrder()->willReturn(3);

        $this->providers[0]->getOffers($company)->willReturn([$offer1]);
        $this->providers[1]->getOffers($company)->willReturn([$offer2, $offer3]);
        
        $offer1->isHidden()->willReturn(false);
        $offer2->isHidden()->willReturn(false);
        $offer3->isHidden()->willReturn(true);

        $this->getOffers($company)->shouldBe([$offer2, $offer1]);
    }
}

<?php

namespace spec\PaymentModule\Loggers;

use BasketModule\ValueObject\MockBasket;
use Entities\Customer;
use Entities\Transaction;
use OrmModule\Repositories\IPersistRepository;
use PaymentModule\Contracts\IPaymentResponse;
use PaymentModule\Dto\CardDetails;
use PaymentModule\Dto\PaymentDetails;
use PaymentModule\Dto\PaymentDetailsFactory;
use PaymentModule\Exceptions\PaymentException;
use PaymentModule\Helpers\Currency;
use PaymentModule\Loggers\IPaymentLogger;
use PaymentModule\Loggers\TransactionPaymentLogger;
use PayPalNVP\Environment;
use PayPalNVP\Response\GetExpressCheckoutDetailsResponse;
use PhpSpec\Exception\Example\FailureException;
use PhpSpec\ObjectBehavior;
use SagePayToken\Token\Exception\DataMismatch;
use SagePayToken\Token\Exception\FailedResponse;
use tests\helpers\PaymentHelper;

/**
 * @mixin TransactionPaymentLogger
 */
class TransactionPaymentLoggerSpec extends ObjectBehavior
{
    function let(IPersistRepository $repository)
    {
        $this->beConstructedWith($repository);
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(TransactionPaymentLogger::class);
        $this->shouldHaveType(IPaymentLogger::class);
    }

    function it_should_create_success_sage_transaction()
    {
        $customer = new Customer('test', 'test');
        $paymentDetails = PaymentDetails::createFromArray(IPaymentResponse::TYPE_SAGE, 10, false, Currency::ISO_GBP, []);
        $paymentResponse = PaymentHelper::createPaymentResponse(IPaymentResponse::TYPE_SAGE);
        $this->createSuccessTransaction($customer, $paymentDetails, $paymentResponse)->shouldBeTransaction([
            'error' => NULL,
            'typeId' => Transaction::TYPE_SAGEPAY,
            'statusId' => Transaction::STATUS_SUCCEEDED,
            'orderCode' => $paymentResponse->getSageVpsTxId()
        ]);
    }

    function it_should_create_success_paypal_transaction()
    {
        $customer = new Customer('test', 'test');
        $paymentDetails = PaymentDetails::createFromArray(IPaymentResponse::TYPE_SAGE, 10, false, Currency::ISO_GBP, []);
        $paymentResponse = PaymentHelper::createPaymentResponse(IPaymentResponse::TYPE_PAYPAL);
        $this->createSuccessTransaction($customer, $paymentDetails, $paymentResponse)->shouldBeTransaction([
            'error' => NULL,
            'typeId' => Transaction::TYPE_PAYPAL,
            'statusId' => Transaction::STATUS_SUCCEEDED,
            'orderCode' => $paymentResponse->getPaypalTransactionId()
        ]);
    }

    function it_should_create_success_transaction()
    {
        $customer = new Customer('test', 'test');
        $paymentDetails = PaymentDetails::createFromArray(IPaymentResponse::TYPE_SAGE, 10, false, Currency::ISO_GBP, []);
        $paymentResponse = PaymentHelper::createPaymentResponse();
        $this->createSuccessTransaction($customer, $paymentDetails, $paymentResponse)->shouldBeTransaction([
            'error' => NULL,
            'statusId' => Transaction::STATUS_SUCCEEDED,
            'cardHolder' => $paymentResponse->getCardHolder()
        ]);
    }

    function it_should_create_failed_sage_transaction_from_summary()
    {
        $customer = new Customer('test', 'test');
        $customer->setFirstName('Robin');
        $customer->setLastName('Joy');
        $cardDetails = PaymentHelper::createCardDetails();
        $paymentDetails = PaymentDetails::createFromArray(IPaymentResponse::TYPE_SAGE, 10, false, Currency::ISO_GBP, $cardDetails->jsonSerialize());
        $response = PaymentHelper::createFailedResponse();
        $summary = PaymentHelper::createSummary();
        $e = FailedResponse::fromResponse($response, $summary);
        $this->createFailedTransaction($e, $customer, $paymentDetails)->shouldBeTransaction([
            'error' => $response->getStatusDetail(),
            'statusId' => Transaction::STATUS_FAILED,
            'cardHolder' => $cardDetails->cardHolder,
            'cardNumber' => $cardDetails->cardNumber,
            'cardType' => $cardDetails->cardType,
            'vendorTxCode' => $summary->getVendorTxCode(),
            'vpsTxId' => $summary->getVpsTxId()
        ]);
    }

    function it_should_create_failed_sage_transaction_from_payment_details()
    {
        $basket = MockBasket::fromAmount(0.02);
        $customer = new Customer('test', 'test');
        $customer->setFirstName('Robin');
        $customer->setLastName('Joy');
        $paymentDetails = PaymentDetailsFactory::create($customer, $basket->getTotalPrice(), $basket->isUsingCredit());
        $response = PaymentHelper::createFailedResponse();
        $summary = PaymentHelper::createSummary();
        $e = FailedResponse::fromResponse($response, $summary);
        $cardDetails = $paymentDetails->getCardDetails();
        $cardDetails->cardNumber = '0044';
        $this->createFailedTransaction($e, $customer, $paymentDetails)->shouldBeTransaction([
            'error' => $response->getStatusDetail(),
            'statusId' => Transaction::STATUS_FAILED,
            'cardHolder' => $cardDetails->cardHolder,
            'cardNumber' => $cardDetails->cardNumber,
            'cardType' => $cardDetails->cardType,
            'vendorTxCode' => $summary->getVendorTxCode(),
            'vpsTxId' => $summary->getVpsTxId()
        ]);
    }

    function it_should_create_failed_paypal_transaction()
    {
        $customer = new Customer('test', 'test');
        $paymentDetails = PaymentDetails::createFromArray(IPaymentResponse::TYPE_SAGE, 10, false, Currency::ISO_GBP, []);
        $e = new PaymentException('failed');
        $this->createFailedTransaction($e, $customer, $paymentDetails)->shouldBeTransaction([
            'error' => 'failed',
            'statusId' => Transaction::STATUS_FAILED
        ]);
    }

    function it_should_create_failed_transaction()
    {
        $customer = new Customer('test', 'test');
        $paymentDetails = PaymentDetails::createFromArray(IPaymentResponse::TYPE_SAGE, 10, false, Currency::ISO_GBP, []);
        $e = new DataMismatch('failed');
        $this->createFailedTransaction($e, $customer, $paymentDetails)->shouldBeTransaction([
            'error' => 'failed',
            'statusId' => Transaction::STATUS_FAILED,
        ]);
    }

    function getMatchers(): array
    {
        return [
            'beTransaction' => function($subject, array $fields) {
                foreach ($fields as $name => $value) {
                    $objectValue = $subject->{'get' . ucfirst($name)}();
                    if ($objectValue !== $value) {
                        throw new FailureException(sprintf(
                            'Transaction "%s" does not match %s with %s',
                            $name, $objectValue, $value
                        ));
                    }
                }
                return TRUE;
            }
        ];
    }

}

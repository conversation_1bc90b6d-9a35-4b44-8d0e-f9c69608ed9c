<?php

namespace spec\BankingModule\TideModule\Creators;

use BankingModule\BankingService;
use BankingModule\Entities\CompanyCustomer;
use BankingModule\TideModule\Creators\ApplicationCreator;
use BankingModule\TideModule\Dto\ApplicationData;
use BankingModule\TideModule\Factories\ApplicationFactory;
use Entities\Company;
use PhpSpec\ObjectBehavior;

class ApplicationCreatorSpec extends ObjectBehavior
{
    /**
     * @var BankingService
     */
    private $bankingService;

    /**
     * @var ApplicationFactory
     */
    private $applicationFactory;

    function let(BankingService $bankingService, ApplicationFactory $applicationFactory)
    {
        $this->bankingService = $bankingService;
        $this->applicationFactory = $applicationFactory;

        $this->beConstructedWith($bankingService, $applicationFactory);
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(ApplicationCreator::class);
    }

    function it_should_create_an_application(Company $company, ApplicationData $applicationData, CompanyCustomer $application)
    {
        $this->applicationFactory->create($company, $applicationData)->willReturn($application);
        $this->bankingService->saveDetails($application)->shouldBeCalled();

        $this->create($company, $applicationData)->shouldReturn($application);
    }
}

<?php

namespace spec\BankingModule\TideModule\Providers;

use BankingModule\TideModule\Providers\MemberDataProvider;
use CompaniesHouseModule\Entities\Address;
use CompaniesHouseModule\Entities\PersonalDetails;
use PhpSpec\ObjectBehavior;
use Utils\Date;

class MemberDataProviderSpec extends ObjectBehavior
{
    private $data = [
        'firstName' => 'Albert',
        'lastName' => 'Einstein',
        'day' => '01',
        'month' => '02',
        'year' => '1998',
        'premise' => '1',
        'street' => 'Wenlock Road',
        'thoroughfare' => 'Thoroughfare',
        'town' => 'London',
        'postcode' => 'N1 7GU'
    ];

    function it_is_initializable()
    {
        $this->shouldHaveType(MemberDataProvider::class);
    }

    function it_should_return_member_data_without_thoroughfare()
    {
        $data = $this->data;

        $details = new PersonalDetails(
            $data['firstName'], $data['lastName'],
            new Date($data['year'] . $data['month'] . $data['day'])
        );
        $address = new Address($data['premise'], $data['street'], $data['town'], $data['postcode']);

        $this->getData($details, $address)->shouldBe([
            'fullName' => $data['firstName'] . ' ' . $data['lastName'],
            'dateOfBirth' => [
                'day' => $data['day'],
                'month' => $data['month'],
                'year' => $data['year']
            ],
            'address' => [
                'addressLine1' => $data['premise'] . ' ' . $data['street'],
                'addressLine2' => $data['town'],
                'postcode' => $data['postcode']
            ]
        ]);
    }

    function it_should_return_member_data_with_thoroughfare()
    {
        $data = $this->data;

        $details = new PersonalDetails(
            $data['firstName'], $data['lastName'],
            new Date($data['year'] . $data['month'] . $data['day'])
        );
        $address = new Address($data['premise'], $data['street'], $data['town'], $data['postcode'], NULL, $data['thoroughfare']);


        $this->getData($details, $address)->shouldBe([
            'fullName' => $data['firstName'] . ' ' . $data['lastName'],
            'dateOfBirth' => [
                'day' => $data['day'],
                'month' => $data['month'],
                'year' => $data['year']
            ],
            'address' => [
                'addressLine1' => $data['premise'] . ' ' . $data['street'],
                'addressLine2' => $data['thoroughfare'],
                'addressLine3' => $data['town'],
                'postcode' => $data['postcode']
            ]
        ]);
    }

    function it_should_handle_missing_dob()
    {
        $data = $this->data;

        $details = new PersonalDetails(
            $data['firstName'], $data['lastName']
        );
        $address = new Address($data['premise'], $data['street'], $data['town'], $data['postcode'], NULL, $data['thoroughfare']);

        $this->getData($details, $address)->shouldBe([
            'fullName' => $data['firstName'] . ' ' . $data['lastName'],
            'dateOfBirth' => NULL,
            'address' => [
                'addressLine1' => $data['premise'] . ' ' . $data['street'],
                'addressLine2' => $data['thoroughfare'],
                'addressLine3' => $data['town'],
                'postcode' => $data['postcode']
            ]
        ]);
    }
}

<?php

declare(strict_types=1);

namespace spec\CompanyIncorporationModule\UseCase\BusinessServices\Api\SkipAdverts;

use BusinessDataModule\Repositories\LeadDataRemover;
use CompanyIncorporationModule\Services\BusinessServices\AdvertService;
use CompanyIncorporationModule\UseCase\BusinessServices\Api\SkipAdverts\Command;
use CompanyIncorporationModule\UseCase\BusinessServices\Api\SkipAdverts\Factory;
use CompanyIncorporationModule\UseCase\BusinessServices\Api\SkipAdverts\Request;
use CompanyIncorporationModule\UseCase\BusinessServices\Api\SkipAdverts\Response;
use Entities\Company;
use Entities\Customer;
use PhpSpec\ObjectBehavior;
use PhpSpec\Wrapper\Collaborator;
use Services\EventService;

class CommandSpec extends ObjectBehavior
{
    private Collaborator|Factory $factory;
    private Collaborator|AdvertService $advertService;
    private Collaborator|EventService $eventService;

    public function let(
        Factory $factory,
        AdvertService $advertService,
        EventService $eventService,
    ): void {
        $this->factory = $factory;
        $this->advertService = $advertService;
        $this->eventService = $eventService;

        $this->beConstructedWith($factory, $advertService, $eventService);
    }

    public function it_is_initializable(): void
    {
        $this->shouldHaveType(Command::class);
    }

    public function it_executes_and_notifies_when_advert_is_skipped(
        Response $response,
    ): void {
        $company = new Company(new Customer('test', 'test'), 'test');
        $request = new Request($company);

        $this->advertService->skipAdvert($company)->willReturn(true);

        $this->eventService
            ->notify(LeadDataRemover::LEAD_REMOVED_OFFER_SKIPPED_EVENT, $company->getId())
            ->shouldBeCalled();

        $this->factory->makeResponse()->willReturn($response);

        $this->execute($request)->shouldReturn($response);
    }

    public function it_executes_without_notifying_when_advert_is_not_skipped(
        Response $response,
    ): void {
        $company = new Company(new Customer('test', 'test'), 'test');
        $request = new Request($company);

        $this->advertService->skipAdvert($company)->willReturn(false);

        $this->eventService
            ->notify(LeadDataRemover::LEAD_REMOVED_OFFER_SKIPPED_EVENT, $company->getId())
            ->shouldNotBeCalled();

        $this->factory->makeResponse()->willReturn($response);

        $this->execute($request)->shouldReturn($response);
    }
}

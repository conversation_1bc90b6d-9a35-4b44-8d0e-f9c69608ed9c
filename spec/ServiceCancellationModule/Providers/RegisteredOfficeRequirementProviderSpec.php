<?php

namespace spec\ServiceCancellationModule\Providers;

use CompaniesHouse\CompaniesHouse;
use CompaniesHouse\Entities\CompanyDetails\Address;
use CompaniesHouse\Entities\CompanyDetails\CompanyDetails;
use Entities\Company;
use ServiceCancellationModule\Entities\RegisteredOfficeResult;
use ServiceCancellationModule\Entities\Requirement;
use ServiceCancellationModule\Providers\RegisteredOfficeRequirementProvider;
use PhpSpec\ObjectBehavior;
use ServiceCancellationModule\Validators\PostcodeValidator;

class RegisteredOfficeRequirementProviderSpec extends ObjectBehavior
{
    /**
     * @var CompaniesHouse
     */
    private $companiesHouse;

    /**
     * @var PostcodeValidator
     */
    private $validator;

    function let(CompaniesHouse $companiesHouse, PostcodeValidator $validator)
    {
        $this->beConstructedWith($companiesHouse, $validator);
        $this->companiesHouse = $companiesHouse;
        $this->validator = $validator;
    }
    
    function it_is_initializable()
    {
        $this->shouldHaveType(RegisteredOfficeRequirementProvider::class);
    }
    
    function it_should_return_requirement(Company $company, CompanyDetails $companyDetails, Address $address, RegisteredOfficeResult $result)
    {
        $company->getNumber()->willReturn('**********');
        $this->companiesHouse->getCompanyDetails('**********')->willReturn($companyDetails);
        $companyDetails->getAddress()->willReturn($address);
        $this->validator->validate($address, $company)->willReturn(TRUE);
        $companyDetails->isDissolved()->willReturn(FALSE);
        
        $requirement = $this->getRequirement($company);
        $requirement->shouldBeAnInstanceOf(Requirement::class);
        $requirement->getType()->shouldBe(Requirement::TYPE_REGISTERED_OFFICE);
        $requirement->getResults()->shouldBeArray();
        $requirement->getStatus()->shouldBe(Requirement::STATUS_VALID);
    }
}

<?php

namespace spec\ServiceCancellationModule\Facades;

use Entities\Service;
use Repositories\ServiceSettingsRepository;
use ServiceCancellationModule\Dto\ReasonData;
use ServiceCancellationModule\Entities\Reason;
use ServiceCancellationModule\Facades\CancellationFacade;
use PhpSpec\ObjectBehavior;
use Prophecy\Argument;
use ServiceCancellationModule\Mailers\CancellationMailer;

class CancellationFacadeSpec extends ObjectBehavior
{
    /**
     * @var ServiceSettingsRepository
     */
    private $serviceSettingsRepository;

    /**
     * @var CancellationMailer
     */
    private $cancellationMailer;
    
    function let(ServiceSettingsRepository $serviceSettingsRepository, CancellationMailer $cancellationMailer)
    {
        $this->beConstructedWith($serviceSettingsRepository, $cancellationMailer);
    }
    
    function it_is_initializable()
    {
        $this->shouldHaveType(CancellationFacade::class);
    }
    
    function service_should_be_cancelled(Service $service, ReasonData $reasonData, Reason $reason)
    {
        $setting = $this->serviceSettingsRepository->getSettingsByService($service)->shouldBeCalled();
        $reasonData->getReason()->shouldBeCalled();
        $reason->getKey()->shouldBeCalled();
        
        $setting->setCancellationReason($reason->getKey())->shouldBeCalled();
        $setting->setCancellationCustomReason($reasonData->getCustomReason())->shouldBeCalled();
        $setting->setCancellationCustomReason($reasonData->getCustomReason())->shouldBeCalled();
        
        $this->cancelService($service, $reasonData);
    }
}

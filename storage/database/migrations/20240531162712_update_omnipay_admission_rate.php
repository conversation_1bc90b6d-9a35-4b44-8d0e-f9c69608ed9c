<?php

use Phinx\Migration\AbstractMigration;

/**
 * The omnipay rate is already set to 100 in production,
 * this migration is being deployed just so that
 * all local and development environments are in sync.
 */
class UpdateOmnipayAdmissionRate extends AbstractMigration
{
    const TBL_SETTINGS = 'cms2_settings';
    const OMNIPAY_RATE_KEY = 'omnipayrate';
    const OMNIPAY_RATE_VALUE = 100;

    public function up()
    {
        $this->execute(
            sprintf(
                "UPDATE %s SET `value`='%s' WHERE `key`='%s';",
                self::TBL_SETTINGS,
                self::OMNIPAY_RATE_VALUE,
                self::OMNIPAY_RATE_KEY
            )
        );
    }

    /**
     * down() is equal to up() because we
     * do not want to change the value of
     * the omnipay rate accidentally by
     * rolling back this migration.
     */
    public function down()
    {
        $this->execute(
            sprintf(
                "UPDATE %s SET `value`='%s' WHERE `key`='%s';",
                self::TBL_SETTINGS,
                self::OMNIPAY_RATE_VALUE,
                self::OMNIPAY_RATE_KEY
            )
        );
    }
}



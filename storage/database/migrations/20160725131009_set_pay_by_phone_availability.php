<?php

use Phinx\Migration\AbstractMigration;

class SetPayByPhoneAvailability extends AbstractMigration
{
    private $companyFormation = [
        1313,
        1314,
        1315,
        1316,
        1317,
        1668,
        1694,
        1175,
        379,
        436,
        400,
        1430,
        1598,
        1022,
        898,
        899,
        921,
        955,
    ];

    private $renewals = [
        1353,
        342,
        1493,
        806,
        334,
        339,
        337,
        401,
        1693,
    ];

    private $otherProducts = [
        340,
        666,
        1609,
        843,
        844,
        309,
        299,
        298,
        1674,
        306,
        882,
        115,
        287,
        1107,
        292,
        293,
        1654,
        1655,
        301,
        302,
        331,
        567,
        315,
        314,
        313,
        311,
        317,
        318,
        319,
    ];

    public function up()
    {
        foreach ($this->companyFormation as $id) {
            $this->execute("
            INSERT INTO cms2_properties (nodeId, name, value, authorId, editorId, dtc, dtm)
            VALUES ('{$id}', 'availableForPayByPhone', 
            '" . BasketProduct::AVAILABLE_FOR_PAY_BY_PHONE_COMPANY_FORMATION ."',
            1, 1, NOW(), NOW()
            )
            ");
        }

        foreach ($this->renewals as $id) {
            $this->execute("
            INSERT INTO cms2_properties (nodeId, name, value, authorId, editorId, dtc, dtm)
            VALUES ('{$id}', 'availableForPayByPhone', 
            '" . BasketProduct::AVAILABLE_FOR_PAY_BY_PHONE_RENEWALS ."',
            1, 1, NOW(), NOW()
            )
            ");
        }

        foreach ($this->otherProducts as $id) {
            $this->execute("
            INSERT INTO cms2_properties (nodeId, name, value, authorId, editorId, dtc, dtm)
            VALUES ('{$id}', 'availableForPayByPhone', 
            '" . BasketProduct::AVAILABLE_FOR_PAY_BY_PHONE_OTHER_PRODUCTS ."',
            1, 1, NOW(), NOW()
            )
            ");
        }
    }

    public function down()
    {
        $this->execute("
            DELETE FROM cms2_properties
            WHERE `name` = 'availableForPayByPhone'
        ");
    }
}

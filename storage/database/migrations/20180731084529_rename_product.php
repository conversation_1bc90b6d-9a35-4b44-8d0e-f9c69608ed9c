<?php

use BusinessServicesModule\Entities\Lead;
use Phinx\Migration\AbstractMigration;

class RenameProduct extends AbstractMigration
{
    public function up()
    {
        $product = new Package(Package::PACKAGE_INTERNATIONAL);
        $product->page->setTitle('International Package');
        $product->page->save();
        $product->save();
    }

    public function down()
    {
        $product = new Package(Package::PACKAGE_INTERNATIONAL);
        $product->page->setTitle('Classic International Package - UK Company Formation ');
        $product->page->save();
        $product->save();
    }
}

<?php

namespace CMS\Proxy\__CG__\Entities\CompanyHouse\FormSubmission\AnnualReturn;


/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Officer extends \Entities\CompanyHouse\FormSubmission\AnnualReturn\Officer implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }

    /**
     * {@inheritDoc}
     * @param string $name
     */
    public function & __get($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__get', [$name]);
        return parent::__get($name);
    }

    /**
     * {@inheritDoc}
     * @param string $name
     * @param mixed  $value
     */
    public function __set($name, $value)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__set', [$name, $value]);
        return parent::__set($name, $value);
    }

    /**
     * {@inheritDoc}
     * @param  string $name
     * @return boolean
     */
    public function __isset($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__isset', [$name]);

        return parent::__isset($name);
    }

    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'officerId', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'formSubmissionId', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'type', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'corporate', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'designatedInd', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'title', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'forename', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'middleName', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'surname', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'corporateName', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'premise', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'street', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'thoroughfare', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'postTown', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'county', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'country', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'postcode', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'careOfName', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'poBox', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'sameAsRegOffice', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'previousNames', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'dob', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'nationality', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'occupation', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'countryOfResidence', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'identificationType', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'placeRegistered', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'registrationNumber', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'lawGoverned', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'legalForm', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'ukNotSynced', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'ukComplete', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'countryNotSynced', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'countryOfResidenceNotSynced', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'annualReturn'];
        }

        return ['__isInitialized__', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'officerId', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'formSubmissionId', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'type', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'corporate', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'designatedInd', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'title', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'forename', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'middleName', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'surname', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'corporateName', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'premise', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'street', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'thoroughfare', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'postTown', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'county', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'country', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'postcode', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'careOfName', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'poBox', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'sameAsRegOffice', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'previousNames', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'dob', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'nationality', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'occupation', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'countryOfResidence', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'identificationType', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'placeRegistered', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'registrationNumber', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'lawGoverned', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'legalForm', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'ukNotSynced', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'ukComplete', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'countryNotSynced', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'countryOfResidenceNotSynced', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\AnnualReturn\\Officer' . "\0" . 'annualReturn'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Officer $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load(): void
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized(): bool
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized): void
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null): void
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer(): ?\Closure
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null): void
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner(): ?\Closure
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties(): array
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getAnnualReturn()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAnnualReturn', []);

        return parent::getAnnualReturn();
    }

    /**
     * {@inheritDoc}
     */
    public function setAnnualReturn(\Entities\CompanyHouse\FormSubmission\AnnualReturn $annualReturn)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAnnualReturn', [$annualReturn]);

        return parent::setAnnualReturn($annualReturn);
    }

    /**
     * {@inheritDoc}
     */
    public function getOfficerId()
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getOfficerId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOfficerId', []);

        return parent::getOfficerId();
    }

    /**
     * {@inheritDoc}
     */
    public function getFormSubmissionId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFormSubmissionId', []);

        return parent::getFormSubmissionId();
    }

    /**
     * {@inheritDoc}
     */
    public function setFormSubmissionId($formSubmissionId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFormSubmissionId', [$formSubmissionId]);

        return parent::setFormSubmissionId($formSubmissionId);
    }

    /**
     * {@inheritDoc}
     */
    public function getType()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getType', []);

        return parent::getType();
    }

    /**
     * {@inheritDoc}
     */
    public function setType($type)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setType', [$type]);

        return parent::setType($type);
    }

    /**
     * {@inheritDoc}
     */
    public function getCorporate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCorporate', []);

        return parent::getCorporate();
    }

    /**
     * {@inheritDoc}
     */
    public function setCorporate($corporate)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCorporate', [$corporate]);

        return parent::setCorporate($corporate);
    }

    /**
     * {@inheritDoc}
     */
    public function getDesignatedInd()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDesignatedInd', []);

        return parent::getDesignatedInd();
    }

    /**
     * {@inheritDoc}
     */
    public function setDesignatedInd($designatedInd)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDesignatedInd', [$designatedInd]);

        return parent::setDesignatedInd($designatedInd);
    }

    /**
     * {@inheritDoc}
     */
    public function getTitle()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTitle', []);

        return parent::getTitle();
    }

    /**
     * {@inheritDoc}
     */
    public function setTitle($title)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTitle', [$title]);

        return parent::setTitle($title);
    }

    /**
     * {@inheritDoc}
     */
    public function getForename()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getForename', []);

        return parent::getForename();
    }

    /**
     * {@inheritDoc}
     */
    public function setForename($forename)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setForename', [$forename]);

        return parent::setForename($forename);
    }

    /**
     * {@inheritDoc}
     */
    public function getMiddleName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMiddleName', []);

        return parent::getMiddleName();
    }

    /**
     * {@inheritDoc}
     */
    public function setMiddleName($middleName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMiddleName', [$middleName]);

        return parent::setMiddleName($middleName);
    }

    /**
     * {@inheritDoc}
     */
    public function getSurname()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSurname', []);

        return parent::getSurname();
    }

    /**
     * {@inheritDoc}
     */
    public function setSurname($surname)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSurname', [$surname]);

        return parent::setSurname($surname);
    }

    /**
     * {@inheritDoc}
     */
    public function getCorporateName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCorporateName', []);

        return parent::getCorporateName();
    }

    /**
     * {@inheritDoc}
     */
    public function setCorporateName($corporateName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCorporateName', [$corporateName]);

        return parent::setCorporateName($corporateName);
    }

    /**
     * {@inheritDoc}
     */
    public function getPremise()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPremise', []);

        return parent::getPremise();
    }

    /**
     * {@inheritDoc}
     */
    public function setPremise($premise)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPremise', [$premise]);

        return parent::setPremise($premise);
    }

    /**
     * {@inheritDoc}
     */
    public function getStreet()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStreet', []);

        return parent::getStreet();
    }

    /**
     * {@inheritDoc}
     */
    public function setStreet($street)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setStreet', [$street]);

        return parent::setStreet($street);
    }

    /**
     * {@inheritDoc}
     */
    public function getThoroughfare()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getThoroughfare', []);

        return parent::getThoroughfare();
    }

    /**
     * {@inheritDoc}
     */
    public function setThoroughfare($thoroughfare)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setThoroughfare', [$thoroughfare]);

        return parent::setThoroughfare($thoroughfare);
    }

    /**
     * {@inheritDoc}
     */
    public function getPostTown()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPostTown', []);

        return parent::getPostTown();
    }

    /**
     * {@inheritDoc}
     */
    public function setPostTown($postTown)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPostTown', [$postTown]);

        return parent::setPostTown($postTown);
    }

    /**
     * {@inheritDoc}
     */
    public function getCounty()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCounty', []);

        return parent::getCounty();
    }

    /**
     * {@inheritDoc}
     */
    public function setCounty($county)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCounty', [$county]);

        return parent::setCounty($county);
    }

    /**
     * {@inheritDoc}
     */
    public function getCountry()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCountry', []);

        return parent::getCountry();
    }

    /**
     * {@inheritDoc}
     */
    public function setCountry($country)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCountry', [$country]);

        return parent::setCountry($country);
    }

    /**
     * {@inheritDoc}
     */
    public function getPostcode()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPostcode', []);

        return parent::getPostcode();
    }

    /**
     * {@inheritDoc}
     */
    public function setPostcode($postcode)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPostcode', [$postcode]);

        return parent::setPostcode($postcode);
    }

    /**
     * {@inheritDoc}
     */
    public function getCareOfName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCareOfName', []);

        return parent::getCareOfName();
    }

    /**
     * {@inheritDoc}
     */
    public function setCareOfName($careOfName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCareOfName', [$careOfName]);

        return parent::setCareOfName($careOfName);
    }

    /**
     * {@inheritDoc}
     */
    public function getPoBox()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPoBox', []);

        return parent::getPoBox();
    }

    /**
     * {@inheritDoc}
     */
    public function setPoBox($poBox)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPoBox', [$poBox]);

        return parent::setPoBox($poBox);
    }

    /**
     * {@inheritDoc}
     */
    public function getSameAsRegOffice()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSameAsRegOffice', []);

        return parent::getSameAsRegOffice();
    }

    /**
     * {@inheritDoc}
     */
    public function setSameAsRegOffice($sameAsRegOffice)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSameAsRegOffice', [$sameAsRegOffice]);

        return parent::setSameAsRegOffice($sameAsRegOffice);
    }

    /**
     * {@inheritDoc}
     */
    public function getPreviousNames()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPreviousNames', []);

        return parent::getPreviousNames();
    }

    /**
     * {@inheritDoc}
     */
    public function setPreviousNames($previousNames)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPreviousNames', [$previousNames]);

        return parent::setPreviousNames($previousNames);
    }

    /**
     * {@inheritDoc}
     */
    public function getDob()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDob', []);

        return parent::getDob();
    }

    /**
     * {@inheritDoc}
     */
    public function setDob($dob)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDob', [$dob]);

        return parent::setDob($dob);
    }

    /**
     * {@inheritDoc}
     */
    public function getNationality()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNationality', []);

        return parent::getNationality();
    }

    /**
     * {@inheritDoc}
     */
    public function setNationality($nationality)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNationality', [$nationality]);

        return parent::setNationality($nationality);
    }

    /**
     * {@inheritDoc}
     */
    public function getOccupation()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOccupation', []);

        return parent::getOccupation();
    }

    /**
     * {@inheritDoc}
     */
    public function setOccupation($occupation)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOccupation', [$occupation]);

        return parent::setOccupation($occupation);
    }

    /**
     * {@inheritDoc}
     */
    public function getCountryOfResidence()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCountryOfResidence', []);

        return parent::getCountryOfResidence();
    }

    /**
     * {@inheritDoc}
     */
    public function setCountryOfResidence($countryOfResidence)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCountryOfResidence', [$countryOfResidence]);

        return parent::setCountryOfResidence($countryOfResidence);
    }

    /**
     * {@inheritDoc}
     */
    public function getIdentificationType()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIdentificationType', []);

        return parent::getIdentificationType();
    }

    /**
     * {@inheritDoc}
     */
    public function setIdentificationType($identificationType)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIdentificationType', [$identificationType]);

        return parent::setIdentificationType($identificationType);
    }

    /**
     * {@inheritDoc}
     */
    public function getPlaceRegistered()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPlaceRegistered', []);

        return parent::getPlaceRegistered();
    }

    /**
     * {@inheritDoc}
     */
    public function setPlaceRegistered($placeRegistered)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPlaceRegistered', [$placeRegistered]);

        return parent::setPlaceRegistered($placeRegistered);
    }

    /**
     * {@inheritDoc}
     */
    public function getRegistrationNumber()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRegistrationNumber', []);

        return parent::getRegistrationNumber();
    }

    /**
     * {@inheritDoc}
     */
    public function setRegistrationNumber($registrationNumber)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRegistrationNumber', [$registrationNumber]);

        return parent::setRegistrationNumber($registrationNumber);
    }

    /**
     * {@inheritDoc}
     */
    public function getLawGoverned()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLawGoverned', []);

        return parent::getLawGoverned();
    }

    /**
     * {@inheritDoc}
     */
    public function setLawGoverned($lawGoverned)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLawGoverned', [$lawGoverned]);

        return parent::setLawGoverned($lawGoverned);
    }

    /**
     * {@inheritDoc}
     */
    public function getLegalForm()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLegalForm', []);

        return parent::getLegalForm();
    }

    /**
     * {@inheritDoc}
     */
    public function setLegalForm($legalForm)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLegalForm', [$legalForm]);

        return parent::setLegalForm($legalForm);
    }

    /**
     * {@inheritDoc}
     */
    public function getUkNotSynced()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUkNotSynced', []);

        return parent::getUkNotSynced();
    }

    /**
     * {@inheritDoc}
     */
    public function setUkNotSynced($ukNotSynced)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUkNotSynced', [$ukNotSynced]);

        return parent::setUkNotSynced($ukNotSynced);
    }

    /**
     * {@inheritDoc}
     */
    public function getUkComplete()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUkComplete', []);

        return parent::getUkComplete();
    }

    /**
     * {@inheritDoc}
     */
    public function setUkComplete($ukComplete)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUkComplete', [$ukComplete]);

        return parent::setUkComplete($ukComplete);
    }

    /**
     * {@inheritDoc}
     */
    public function getCountryNotSynced()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCountryNotSynced', []);

        return parent::getCountryNotSynced();
    }

    /**
     * {@inheritDoc}
     */
    public function setCountryNotSynced($countryNotSynced)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCountryNotSynced', [$countryNotSynced]);

        return parent::setCountryNotSynced($countryNotSynced);
    }

    /**
     * {@inheritDoc}
     */
    public function getCountryOfResidenceNotSynced()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCountryOfResidenceNotSynced', []);

        return parent::getCountryOfResidenceNotSynced();
    }

    /**
     * {@inheritDoc}
     */
    public function setCountryOfResidenceNotSynced($countryOfResidenceNotSynced)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCountryOfResidenceNotSynced', [$countryOfResidenceNotSynced]);

        return parent::setCountryOfResidenceNotSynced($countryOfResidenceNotSynced);
    }

    /**
     * {@inheritDoc}
     */
    public function __call($name, $args)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__call', [$name, $args]);

        return parent::__call($name, $args);
    }

    /**
     * {@inheritDoc}
     */
    public function __unset($name)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__unset', [$name]);

        return parent::__unset($name);
    }

}
